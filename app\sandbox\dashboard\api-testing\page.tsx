"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { <PERSON><PERSON>, <PERSON>, RefreshC<PERSON>, Zap, AlertTriangle, Clock, Server, ArrowRight, Key } from "lucide-react"

// Request to pay form schema
const requestToPaySchema = z.object({
  apiKey: z.string().min(1, "API key is required"),
  phoneNumber: z.string()
    .min(9, "Phone number must be at least 9 digits")
    .regex(/^237[0-9]{8}$/, "Phone number must be in format 237XXXXXXXX"),
  amount: z.string()
    .min(1, "Amount is required")
    .regex(/^[0-9]+$/, "Amount must be a number")
    .refine(val => parseInt(val) >= 100, "Amount must be at least 100 XAF"),
  externalId: z.string().min(1, "External ID is required"),
  description: z.string().min(3, "Description must be at least 3 characters"),
})

// Status check form schema
const statusCheckSchema = z.object({
  apiKey: z.string().min(1, "API key is required"),
  transactionId: z.string().min(1, "Transaction ID is required"),
})

// API response types
type TransactionStatus = "PENDING" | "SUCCESSFUL" | "FAILED" | "TIMEOUT"

interface ApiKey {
  id: string
  keyName: string
  environment: string
  isActive: boolean
  usageCount: number
  rateLimit: number
  createdAt: string
  lastUsedAt: string | null
}

interface ApiResponse {
  success: boolean
  data?: {
    transactionId?: string
    id?: string
    externalId: string
    amount: number
    currency: string
    status: TransactionStatus
    payerPhone: string
    payerMessage?: string
    payeeNote?: string
    createdAt: string
    completedAt?: string | null
    failureReason?: string | null
  }
  error?: string
  code?: string
}

export default function ApiTestingPage() {
  const [activeTab, setActiveTab] = useState("request-to-pay")
  const [apiResponse, setApiResponse] = useState<ApiResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [copied, setCopied] = useState(false)
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([])
  const [loadingApiKeys, setLoadingApiKeys] = useState(true)

  // Request to pay form
  const requestToPayForm = useForm<z.infer<typeof requestToPaySchema>>({
    resolver: zodResolver(requestToPaySchema),
    defaultValues: {
      apiKey: "",
      phoneNumber: "237612345678",
      amount: "1000",
      externalId: 'ext_test_sample_id',
      description: "Test payment request",
    },
  })

  // Status check form
  const statusCheckForm = useForm<z.infer<typeof statusCheckSchema>>({
    resolver: zodResolver(statusCheckSchema),
    defaultValues: {
      apiKey: "",
      transactionId: "",
    },
  })

  // Load API keys on component mount
  useEffect(() => {
    loadApiKeys()
  }, [])

  const loadApiKeys = async () => {
    try {
      setLoadingApiKeys(true)
      const response = await fetch('/api/sandbox/api-keys')
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setApiKeys(result.data)
          // Auto-select first active API key
          const activeKey = result.data.find((key: ApiKey) => key.isActive)
          if (activeKey) {
            const apiKeyValue = `sk_sandbox_${activeKey.id}`
            requestToPayForm.setValue("apiKey", apiKeyValue)
            statusCheckForm.setValue("apiKey", apiKeyValue)
          }
        }
      }
    } catch (error) {
      console.error('Failed to load API keys:', error)
    } finally {
      setLoadingApiKeys(false)
    }
  }

  // Generate new external ID
  const generateExternalId = () => {
    // Use client-side only code for random generation
    if (typeof window !== 'undefined') {
      const randomId = 'ext_' + Math.random().toString(36).substring(2, 10) + '_' + Date.now().toString(36)
      requestToPayForm.setValue("externalId", randomId)
    } else {
      requestToPayForm.setValue("externalId", 'ext_test_sample_id')
    }
  }

  // Copy transaction ID to status check
  const copyTransactionIdToStatusCheck = () => {
    const transactionId = apiResponse?.data?.transactionId || apiResponse?.data?.id
    if (transactionId) {
      statusCheckForm.setValue("transactionId", transactionId)
      setActiveTab("status-check")
    }
  }

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  // Handle form submission for request to pay
  const onRequestToPaySubmit = async (values: z.infer<typeof requestToPaySchema>) => {
    setIsLoading(true)
    setApiResponse(null)

    try {
      const response = await fetch('/api/sandbox/collect-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${values.apiKey}`
        },
        body: JSON.stringify({
          amount: parseInt(values.amount),
          currency: 'XAF',
          externalId: values.externalId,
          payerPhone: values.phoneNumber,
          payerMessage: values.description,
          payeeNote: values.description
        })
      })

      const result = await response.json()
      setApiResponse(result)

    } catch (error) {
      console.error('Payment request failed:', error)
      setApiResponse({
        success: false,
        error: 'Network error: Failed to connect to API',
        code: 'NETWORK_ERROR'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle form submission for status check
  const onStatusCheckSubmit = async (values: z.infer<typeof statusCheckSchema>) => {
    setIsLoading(true)
    setApiResponse(null)

    try {
      const response = await fetch(`/api/sandbox/transactions/${values.transactionId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${values.apiKey}`
        }
      })

      const result = await response.json()
      setApiResponse(result)

    } catch (error) {
      console.error('Status check failed:', error)
      setApiResponse({
        success: false,
        error: 'Network error: Failed to connect to API',
        code: 'NETWORK_ERROR'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Get status badge
  const getStatusBadge = (status: TransactionStatus) => {
    switch (status) {
      case "SUCCESSFUL":
        return (
          <Badge className="bg-green-500 text-white">
            <Check className="h-3 w-3 mr-1" /> Success
          </Badge>
        )
      case "FAILED":
        return (
          <Badge className="bg-red-500 text-white">
            <AlertTriangle className="h-3 w-3 mr-1" /> Failed
          </Badge>
        )
      case "PENDING":
        return (
          <Badge className="bg-yellow-500 text-white">
            <Clock className="h-3 w-3 mr-1" /> Pending
          </Badge>
        )
      case "TIMEOUT":
        return (
          <Badge className="bg-gray-500 text-white">
            <Server className="h-3 w-3 mr-1" /> Timeout
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div>
      <div className="mb-6 md:mb-8">
        <h1 className="text-2xl md:text-3xl font-bold">API Testing</h1>
        <p className="text-sm md:text-base text-gray-500">Test MTN MoMo API functionality in the sandbox environment</p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>MTN MoMo API Testing</CardTitle>
              <CardDescription>
                Test payment requests and status checks in the sandbox environment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="request-to-pay">Request to Pay</TabsTrigger>
                  <TabsTrigger value="status-check">Check Status</TabsTrigger>
                </TabsList>

                <TabsContent value="request-to-pay">
                  <Form {...requestToPayForm}>
                    <form onSubmit={requestToPayForm.handleSubmit(onRequestToPaySubmit)} className="space-y-4">
                      <FormField
                        control={requestToPayForm.control}
                        name="apiKey"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>API Key *</FormLabel>
                            <FormControl>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <SelectTrigger>
                                  <SelectValue placeholder={loadingApiKeys ? "Loading API keys..." : "Select an API key"} />
                                </SelectTrigger>
                                <SelectContent>
                                  {apiKeys.filter(key => key.isActive).map((key) => (
                                    <SelectItem key={key.id} value={`sk_sandbox_${key.id}`}>
                                      <div className="flex items-center gap-2">
                                        <Key className="h-4 w-4" />
                                        {key.keyName}
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormDescription>
                              Select your sandbox API key for authentication
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={requestToPayForm.control}
                        name="phoneNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number *</FormLabel>
                            <FormControl>
                              <Input placeholder="237612345678" {...field} />
                            </FormControl>
                            <FormDescription>
                              The phone number to request payment from (format: 237XXXXXXXX)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={requestToPayForm.control}
                        name="amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Amount (XAF) *</FormLabel>
                            <FormControl>
                              <Input type="number" min="100" placeholder="1000" {...field} />
                            </FormControl>
                            <FormDescription>
                              Amount in XAF to request (minimum 100 XAF)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={requestToPayForm.control}
                        name="externalId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>External ID *</FormLabel>
                            <div className="flex gap-2">
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <Button 
                                type="button" 
                                variant="outline" 
                                size="icon"
                                onClick={generateExternalId}
                                title="Generate new UUID"
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                            </div>
                            <FormDescription>
                              Your reference ID for this transaction
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={requestToPayForm.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description *</FormLabel>
                            <FormControl>
                              <Textarea placeholder="Payment description" {...field} />
                            </FormControl>
                            <FormDescription>
                              Description of what this payment is for
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="pt-4">
                        <Button
                          type="submit"
                          className="bg-blue-600 hover:bg-blue-700 text-xs md:text-sm w-full"
                          disabled={isLoading || loadingApiKeys}
                        >
                          {isLoading ? (
                            <>
                              <RefreshCw className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Zap className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                              Initiate Payment Request
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </TabsContent>

                <TabsContent value="status-check">
                  <Form {...statusCheckForm}>
                    <form onSubmit={statusCheckForm.handleSubmit(onStatusCheckSubmit)} className="space-y-4">
                      <FormField
                        control={statusCheckForm.control}
                        name="apiKey"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>API Key *</FormLabel>
                            <FormControl>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <SelectTrigger>
                                  <SelectValue placeholder={loadingApiKeys ? "Loading API keys..." : "Select an API key"} />
                                </SelectTrigger>
                                <SelectContent>
                                  {apiKeys.filter(key => key.isActive).map((key) => (
                                    <SelectItem key={key.id} value={`sk_sandbox_${key.id}`}>
                                      <div className="flex items-center gap-2">
                                        <Key className="h-4 w-4" />
                                        {key.keyName}
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormDescription>
                              Select your sandbox API key for authentication
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={statusCheckForm.control}
                        name="transactionId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Transaction ID *</FormLabel>
                            <FormControl>
                              <Input placeholder="txn_1234567890" {...field} />
                            </FormControl>
                            <FormDescription>
                              The transaction ID returned from the request-to-pay call
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="pt-4">
                        <Button
                          type="submit"
                          className="bg-blue-600 hover:bg-blue-700 text-xs md:text-sm w-full"
                          disabled={isLoading || loadingApiKeys}
                        >
                          {isLoading ? (
                            <>
                              <RefreshCw className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2 animate-spin" />
                              Checking...
                            </>
                          ) : (
                            <>
                              <ArrowRight className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                              Check Payment Status
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </TabsContent>
              </Tabs>

              {/* API Response */}
              {apiResponse && (
                <div className="mt-8">
                  <Separator className="my-4" />
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">API Response</h3>
                      <div className="flex items-center gap-2">
                        {apiResponse.success ? (
                          apiResponse.data?.status ? getStatusBadge(apiResponse.data.status) : (
                            <Badge className="bg-green-500 text-white">
                              <Check className="h-3 w-3 mr-1" /> Success
                            </Badge>
                          )
                        ) : (
                          <Badge className="bg-red-500 text-white">
                            <AlertTriangle className="h-3 w-3 mr-1" /> Error
                          </Badge>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(JSON.stringify(apiResponse, null, 2))}
                        >
                          {copied ? (
                            <>
                              <Check className="h-4 w-4 mr-1" /> Copied
                            </>
                          ) : (
                            <>
                              <Copy className="h-4 w-4 mr-1" /> Copy JSON
                            </>
                          )}
                        </Button>
                      </div>
                    </div>

                    {apiResponse.success && apiResponse.data ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-xs md:text-sm font-semibold mb-2">Transaction Details</h4>
                          <div className="space-y-2">
                            <div className="flex flex-col md:flex-row md:justify-between">
                              <span className="text-xs md:text-sm text-gray-500">Transaction ID:</span>
                              <span className="text-xs md:text-sm font-mono break-all">{apiResponse.data.transactionId || apiResponse.data.id}</span>
                            </div>
                            <div className="flex flex-col md:flex-row md:justify-between">
                              <span className="text-xs md:text-sm text-gray-500">External ID:</span>
                              <span className="text-xs md:text-sm font-mono break-all">{apiResponse.data.externalId}</span>
                            </div>
                            <div className="flex flex-col md:flex-row md:justify-between">
                              <span className="text-xs md:text-sm text-gray-500">Amount:</span>
                              <span className="text-xs md:text-sm font-mono">{apiResponse.data.amount} {apiResponse.data.currency}</span>
                            </div>
                            <div className="flex flex-col md:flex-row md:justify-between">
                              <span className="text-xs md:text-sm text-gray-500">Payer:</span>
                              <span className="text-xs md:text-sm font-mono">{apiResponse.data.payerPhone}</span>
                            </div>
                            <div className="flex flex-col md:flex-row md:justify-between">
                              <span className="text-xs md:text-sm text-gray-500">Status:</span>
                              <span className="text-xs md:text-sm">{getStatusBadge(apiResponse.data.status)}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="text-xs md:text-sm font-semibold mb-2">Response Details</h4>
                          <div className="space-y-2">
                            <div className="flex flex-col md:flex-row md:justify-between">
                              <span className="text-xs md:text-sm text-gray-500">Created At:</span>
                              <span className="text-xs md:text-sm">
                                {(() => {
                                  const date = new Date(apiResponse.data.createdAt);
                                  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
                                })()}
                              </span>
                            </div>
                            <div className="flex flex-col md:flex-row md:justify-between">
                              <span className="text-xs md:text-sm text-gray-500">Completed At:</span>
                              <span className="text-xs md:text-sm">
                                {apiResponse.data.completedAt
                                  ? (() => {
                                      const date = new Date(apiResponse.data.completedAt);
                                      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
                                    })()
                                  : "Pending"}
                              </span>
                            </div>
                            {apiResponse.data.failureReason && (
                              <div className="flex flex-col md:flex-row md:justify-between">
                                <span className="text-xs md:text-sm text-gray-500">Failure Reason:</span>
                                <span className="text-xs md:text-sm text-red-600">{apiResponse.data.failureReason}</span>
                              </div>
                            )}
                            {apiResponse.data.payerMessage && (
                              <div className="flex flex-col md:flex-row md:justify-between">
                                <span className="text-xs md:text-sm text-gray-500">Message:</span>
                                <span className="text-xs md:text-sm">{apiResponse.data.payerMessage}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-red-50 border border-red-200 rounded-md p-4">
                        <h4 className="text-sm font-semibold text-red-800 mb-2">Error Response</h4>
                        <div className="space-y-2">
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">Error:</span>
                            <span className="text-xs md:text-sm text-red-600">{apiResponse.error}</span>
                          </div>
                          {apiResponse.code && (
                            <div className="flex flex-col md:flex-row md:justify-between">
                              <span className="text-xs md:text-sm text-gray-500">Code:</span>
                              <span className="text-xs md:text-sm font-mono text-red-600">{apiResponse.code}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm mt-4">
                      {JSON.stringify(apiResponse, null, 2)}
                    </pre>

                    {apiResponse.success && apiResponse.data && (apiResponse.data.transactionId || apiResponse.data.id) && activeTab === "request-to-pay" && (
                      <div className="flex justify-end">
                        <Button variant="outline" onClick={copyTransactionIdToStatusCheck}>
                          <ArrowRight className="h-4 w-4 mr-2" />
                          Check Status of This Transaction
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          {/* API Information */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>API Endpoints</CardTitle>
              <CardDescription>Sandbox MTN MoMo API endpoints</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-semibold mb-1">Base URL</h3>
                <code className="block p-2 bg-gray-100 rounded-md text-xs font-mono">
                  {typeof window !== 'undefined' ? window.location.origin : 'https://starterpay.cm'}
                </code>
              </div>

              <div>
                <h3 className="text-sm font-semibold mb-1">Request to Pay</h3>
                <code className="block p-2 bg-gray-100 rounded-md text-xs font-mono">
                  POST /api/sandbox/collect-payment
                </code>
              </div>

              <div>
                <h3 className="text-sm font-semibold mb-1">Check Status</h3>
                <code className="block p-2 bg-gray-100 rounded-md text-xs font-mono">
                  GET /api/sandbox/transactions/{"{transactionId}"}
                </code>
              </div>
            </CardContent>
          </Card>

          {/* API Information */}
          <Card>
            <CardHeader>
              <CardTitle>Real MTN MoMo Integration</CardTitle>
              <CardDescription>Test with actual MTN MoMo sandbox API</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert className="bg-blue-50 border-blue-200">
                <Server className="h-4 w-4 text-blue-500" />
                <AlertTitle className="text-blue-800">Live API Testing</AlertTitle>
                <AlertDescription className="text-blue-700 text-xs">
                  This interface connects to real MTN MoMo sandbox APIs. Transactions will be processed through actual MTN systems.
                </AlertDescription>
              </Alert>

              <Alert className="bg-green-50 border-green-200">
                <Check className="h-4 w-4 text-green-500" />
                <AlertTitle className="text-green-800">Sandbox Environment</AlertTitle>
                <AlertDescription className="text-green-700 text-xs">
                  All transactions are in sandbox mode - no real money is transferred. Use test phone numbers for testing.
                </AlertDescription>
              </Alert>

              <Alert className="bg-yellow-50 border-yellow-200">
                <Clock className="h-4 w-4 text-yellow-500" />
                <AlertTitle className="text-yellow-800">Transaction Status</AlertTitle>
                <AlertDescription className="text-yellow-700 text-xs">
                  Real transactions may take time to process. Use the status check feature to monitor transaction progress.
                </AlertDescription>
              </Alert>

              {apiKeys.length === 0 && !loadingApiKeys && (
                <Alert className="bg-orange-50 border-orange-200">
                  <AlertTriangle className="h-4 w-4 text-orange-500" />
                  <AlertTitle className="text-orange-800">No API Keys</AlertTitle>
                  <AlertDescription className="text-orange-700 text-xs">
                    You need to create an API key first. Go to the API Access page to generate one.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}