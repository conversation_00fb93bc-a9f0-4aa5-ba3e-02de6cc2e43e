# StarterPay RBAC Usage Guide

This guide explains how to use the simple Role-Based Access Control (RBAC) system implemented in StarterPay.

## Overview

The RBAC system provides:
- **Middleware protection** for routes
- **API route protection** with decorators
- **React components** for conditional rendering
- **Hooks** for client-side role checking
- **Utility functions** for server-side role validation

## User Roles

- **STUDENT**: Can access student dashboard, manage their profile, API keys, and transactions
- **ADMIN**: Can access admin dashboard, manage all users, review KYC applications

## 1. Middleware Protection

The middleware automatically protects routes based on user roles:

```typescript
// Routes are automatically protected:
// /dashboard -> Students only
// /admin -> Admins only
// /api -> Authenticated users only
```

## 2. API Route Protection

### Basic Authentication
```typescript
import { withAuth } from '@/lib/api-auth'

export const GET = withAuth(async (request, auth) => {
  // auth.user contains: { id, role, email, fullName }
  return NextResponse.json({ message: 'Hello ' + auth.user.fullName })
})
```

### Admin-Only Routes
```typescript
import { withAdminAuth } from '@/lib/api-auth'

export const GET = withAdminAuth(async (request, auth) => {
  // Only admins can access this
  return NextResponse.json({ adminData: 'secret' })
})
```

### Student-Only Routes
```typescript
import { withStudentAuth } from '@/lib/api-auth'

export const GET = withStudentAuth(async (request, auth) => {
  // Only students can access this
  return NextResponse.json({ studentData: 'personal' })
})
```

### Resource Access Control
```typescript
import { requireResourceAccess } from '@/lib/api-auth'

export async function GET(request: NextRequest, { params }: { params: { userId: string } }) {
  try {
    // Ensures user can only access their own resources (or admin can access any)
    const auth = await requireResourceAccess(request, params.userId)
    
    // Proceed with logic...
    return NextResponse.json({ data: 'user-specific' })
  } catch (error) {
    return handleAuthError(error)
  }
}
```

## 3. React Component Protection

### Role Guards
```tsx
import { RoleGuard, AdminOnly, StudentOnly, AuthOnly } from '@/components/auth/role-guard'
import { UserType } from '@prisma/client'

// General role guard
<RoleGuard allowedRoles={[UserType.ADMIN, UserType.STUDENT]} fallback={<div>Access denied</div>}>
  <SensitiveComponent />
</RoleGuard>

// Admin only
<AdminOnly fallback={<div>Admin access required</div>}>
  <AdminPanel />
</AdminOnly>

// Student only
<StudentOnly>
  <StudentDashboard />
</StudentOnly>

// Any authenticated user
<AuthOnly fallback={<LoginPrompt />}>
  <UserContent />
</AuthOnly>
```

## 4. React Hooks

### useAuth Hook
```tsx
import { useAuth } from '@/hooks/use-auth'

function MyComponent() {
  const { user, isLoading, isAuthenticated, isAdmin, isStudent } = useAuth()

  if (isLoading) return <div>Loading...</div>
  if (!isAuthenticated) return <div>Please log in</div>

  return (
    <div>
      <h1>Welcome {user?.fullName}</h1>
      {isAdmin && <AdminButton />}
      {isStudent && <StudentButton />}
    </div>
  )
}
```

### useRoleAccess Hook
```tsx
import { useRoleAccess } from '@/hooks/use-auth'
import { UserType } from '@prisma/client'

function MyComponent() {
  const { canAccess, adminOnly, studentOnly } = useRoleAccess()

  return (
    <div>
      {adminOnly() && <AdminFeature />}
      {studentOnly() && <StudentFeature />}
      {canAccess([UserType.ADMIN, UserType.STUDENT]) && <SharedFeature />}
    </div>
  )
}
```

## 5. Server-Side Utilities

### Basic Role Checking
```typescript
import { getCurrentUser, isAdmin, isStudent, requireAdmin } from '@/lib/rbac'

// Get current user
const user = await getCurrentUser()
if (!user) {
  // Handle unauthenticated
}

// Check roles
if (isAdmin(user.role)) {
  // Admin logic
}

// Require specific role (throws error if not met)
requireAdmin(user.role) // Throws AuthError if not admin
```

### Resource Access
```typescript
import { canAccessUserResource, requireUserResourceAccess } from '@/lib/rbac'

// Check if user can access another user's resource
const canAccess = canAccessUserResource(currentUserId, targetUserId, currentUserRole)

// Require access (throws error if denied)
requireUserResourceAccess(currentUserId, targetUserId, currentUserRole)
```

## 6. Error Handling

The system throws `AuthError` for authorization failures:

```typescript
import { AuthError } from '@/lib/rbac'

try {
  requireAdmin(userRole)
} catch (error) {
  if (error instanceof AuthError) {
    console.log(error.message) // "Access denied. Admin role required."
    console.log(error.code)    // "ADMIN_REQUIRED"
  }
}
```

## 7. Best Practices

1. **Always validate on server-side**: Client-side checks are for UX only
2. **Use middleware for route protection**: Automatic and consistent
3. **Use API decorators**: `withAuth`, `withAdminAuth`, `withStudentAuth`
4. **Check resource ownership**: Use `requireResourceAccess` for user-specific data
5. **Handle errors gracefully**: Use try-catch with `AuthError`

## 8. Common Patterns

### Protected Page Component
```tsx
'use client'
import { useAuth } from '@/hooks/use-auth'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export default function ProtectedPage() {
  const { isAuthenticated, isAdmin, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
    if (!isLoading && isAuthenticated && !isAdmin) {
      router.push('/dashboard') // Redirect non-admins
    }
  }, [isAuthenticated, isAdmin, isLoading, router])

  if (isLoading) return <div>Loading...</div>
  if (!isAuthenticated || !isAdmin) return null

  return <AdminContent />
}
```

### API Route with Resource Protection
```typescript
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const auth = await requireResourceAccess(request, params.userId)
    
    const userData = await prisma.user.findUnique({
      where: { id: params.userId }
    })
    
    return NextResponse.json({ user: userData })
  } catch (error) {
    return handleAuthError(error)
  }
}
```

This simple RBAC system provides robust protection while remaining easy to use and understand.
