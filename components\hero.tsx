import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"

export function Hero() {
  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-white to-primary-100 py-16 sm:py-24 lg:py-32">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="h-full w-full" viewBox="0 0 800 800" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="currentColor" strokeWidth="1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      <div className="container relative px-4 md:px-6">
        <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
          <div className="space-y-6">
            <div className="inline-block rounded-full bg-primary-100 px-3 py-1 text-sm font-medium text-primary-700">
              Built for Cameroonian Tech Students
            </div>
            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl">
              Access MTN MoMo APIs <span className="text-primary-600">Without Business Registration</span>
            </h1>
            <p className="text-lg text-gray-600 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              StarterPay empowers Cameroonian tech students to integrate mobile payments into their projects without the
              barriers of corporate registration.
            </p>
            <div className="flex flex-col gap-3 min-[400px]:flex-row">
              <Button
                size="lg"
                asChild
                className="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 rounded-full px-8"
              >
                <Link href="/register">Get Started</Link>
              </Button>
              <Button size="lg" variant="outline" asChild className="rounded-full px-8">
                <Link href="/docs">View Documentation</Link>
              </Button>
            </div>
          </div>
          <div className="mx-auto lg:ml-auto relative">
            <div className="absolute -inset-1 rounded-xl bg-gradient-to-r from-primary-600 to-secondary blur-xl opacity-30"></div>
            <div className="relative rounded-xl overflow-hidden shadow-2xl">
              <Image
                src="/placeholder.svg?height=400&width=500&text=Mobile+Payment+Integration"
                alt="Mobile Payment Integration"
                width={500}
                height={400}
                className="object-cover"
                priority
              />
            </div>
            <div className="absolute -right-4 -top-4 rounded-lg bg-white p-4 shadow-lg">
              <div className="flex items-center space-x-2">
                <div className="h-3 w-3 rounded-full bg-success"></div>
                <span className="text-sm font-medium">API Connected</span>
              </div>
            </div>
            <div className="absolute -bottom-4 -left-4 rounded-lg bg-white p-4 shadow-lg">
              <div className="flex items-center space-x-2">
                <div className="h-3 w-3 rounded-full bg-accent"></div>
                <span className="text-sm font-medium">Student Verified</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
