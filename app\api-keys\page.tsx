"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { toast } from "@/hooks/use-toast"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function ApiKeysPage() {
  const [apiKey, setApiKey] = useState("sp_sandbox_••••••••••••••••••••••••••••••")
  const [showKey, setShowKey] = useState(false)
  const [isRegenerating, setIsRegenerating] = useState(false)

  // Mock data - would come from API in real implementation
  const apiUsage = { current: 45, limit: 100 }

  const handleCopyKey = () => {
    // In a real implementation, we would get the actual API key from state or API
    const keyToCopy = showKey ? "sp_sandbox_abcdefghijklmnopqrstuvwxyz123456" : apiKey
    navigator.clipboard.writeText(keyToCopy)
    toast({
      title: "API key copied",
      description: "The API key has been copied to your clipboard.",
    })
  }

  const handleRegenerateKey = () => {
    setIsRegenerating(true)

    // This would be replaced with actual API call
    setTimeout(() => {
      setApiKey("sp_sandbox_••••••••••••••••••••••••••••••")
      setShowKey(false)
      setIsRegenerating(false)
      toast({
        title: "API key regenerated",
        description: "Your new API key has been generated successfully.",
      })
    }, 1000)
  }

  const toggleShowKey = () => {
    if (showKey) {
      setApiKey("sp_sandbox_••••••••••••••••••••••••••••••")
      setShowKey(false)
    } else {
      // In a real implementation, we would get the actual API key from an API
      setApiKey("sp_sandbox_abcdefghijklmnopqrstuvwxyz123456")
      setShowKey(true)
    }
  }

  return (
    <div className="container py-6 md:py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">API Keys</h1>
        <p className="text-gray-500">Manage your API keys and view integration guides</p>
      </div>

      <Tabs defaultValue="keys" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="keys">API Keys</TabsTrigger>
          <TabsTrigger value="integration">Integration Guide</TabsTrigger>
        </TabsList>
        <TabsContent value="keys">
          <div className="grid gap-6 lg:grid-cols-2">
            <div className="space-y-6">
              {/* Current Key Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Current API Key</CardTitle>
                  <CardDescription>Your API key for MTN MoMo integration</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="bg-gray-200 text-gray-700">
                      Sandbox
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 rounded-md border bg-gray-50 px-3 py-2 text-sm font-mono">{apiKey}</div>
                    <Button variant="outline" size="sm" onClick={toggleShowKey}>
                      {showKey ? "Hide" : "Show"}
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleCopyKey}>
                      Copy
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        {apiUsage.current}/{apiUsage.limit} calls
                      </span>
                      <span className="text-sm text-gray-500">
                        {Math.round((apiUsage.current / apiUsage.limit) * 100)}%
                      </span>
                    </div>
                    <Progress value={(apiUsage.current / apiUsage.limit) * 100} />
                  </div>
                  <Button variant="outline" onClick={handleRegenerateKey} disabled={isRegenerating} className="w-full">
                    {isRegenerating ? "Regenerating..." : "Regenerate API Key"}
                  </Button>
                </CardContent>
              </Card>

              {/* API Usage Card */}
              <Card>
                <CardHeader>
                  <CardTitle>API Usage</CardTitle>
                  <CardDescription>Your API usage statistics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="rounded-lg border bg-card p-3">
                        <div className="text-sm font-medium text-gray-500">Total Requests</div>
                        <div className="text-2xl font-bold">245</div>
                      </div>
                      <div className="rounded-lg border bg-card p-3">
                        <div className="text-sm font-medium text-gray-500">Success Rate</div>
                        <div className="text-2xl font-bold">98.2%</div>
                      </div>
                      <div className="rounded-lg border bg-card p-3">
                        <div className="text-sm font-medium text-gray-500">Avg. Response Time</div>
                        <div className="text-2xl font-bold">156ms</div>
                      </div>
                      <div className="rounded-lg border bg-card p-3">
                        <div className="text-sm font-medium text-gray-500">Last Used</div>
                        <div className="text-2xl font-bold">2h ago</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Integration Card */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Start</CardTitle>
                <CardDescription>Get started with MTN MoMo integration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">1. Copy your API key</h3>
                    <p className="text-sm text-gray-500">
                      Use the API key shown on the left to authenticate your requests.
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">2. Install the SDK</h3>
                    <div className="rounded-md bg-gray-900 p-4">
                      <pre className="text-sm text-white">
                        <code>npm install starterpay-sdk</code>
                      </pre>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">3. Make your first request</h3>
                    <div className="rounded-md bg-gray-900 p-4">
                      <pre className="text-sm text-white">
                        <code>{`import { StarterPay } from 'starterpay-sdk';

const client = new StarterPay({
  apiKey: 'YOUR_API_KEY',
  environment: 'sandbox'
});

// Request payment
const payment = await client.requestPayment({
  amount: 1000,
  currency: 'XAF',
  phoneNumber: '237612345678',
  description: 'Test payment'
});

console.log(payment);`}</code>
                      </pre>
                    </div>
                  </div>
                </div>
                <Button className="w-full bg-gradient-to-r from-primary-600 to-primary-700">
                  View Full Documentation
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="integration">
          <Card>
            <CardHeader>
              <CardTitle>Integration Guide</CardTitle>
              <CardDescription>Learn how to integrate StarterPay with your application</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-xl font-bold">Getting Started</h3>
                <p className="text-gray-600">
                  Follow these steps to integrate MTN Mobile Money payments into your application using StarterPay.
                </p>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <h4 className="text-lg font-medium">1. Authentication</h4>
                  <p className="text-sm text-gray-500">
                    All API requests require authentication using your API key. Include your API key in the request
                    headers.
                  </p>
                  <div className="rounded-md bg-gray-900 p-4">
                    <pre className="text-sm text-white">
                      <code>{`// Example header
{
  "Authorization": "Bearer YOUR_API_KEY",
  "Content-Type": "application/json"
}`}</code>
                    </pre>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-lg font-medium">2. Request a Payment</h4>
                  <p className="text-sm text-gray-500">
                    To request a payment from a customer, use the following endpoint and parameters.
                  </p>
                  <div className="rounded-md bg-gray-900 p-4">
                    <pre className="text-sm text-white">
                      <code>{`// POST /api/v1/payments/request
{
  "amount": 1000,
  "currency": "XAF",
  "phoneNumber": "237612345678",
  "externalId": "order_123",
  "description": "Payment for Order #123"
}`}</code>
                    </pre>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-lg font-medium">3. Check Payment Status</h4>
                  <p className="text-sm text-gray-500">
                    After requesting a payment, you can check its status using the payment ID.
                  </p>
                  <div className="rounded-md bg-gray-900 p-4">
                    <pre className="text-sm text-white">
                      <code>{`// GET /api/v1/payments/{paymentId}/status
// Response
{
  "status": "SUCCESSFUL",
  "amount": 1000,
  "currency": "XAF",
  "externalId": "order_123",
  "createdAt": "2025-06-12T12:34:56Z"
}`}</code>
                    </pre>
                  </div>
                </div>
              </div>

              <Button className="w-full bg-gradient-to-r from-primary-600 to-primary-700">
                View Complete API Reference
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
