# StarterPay System Architecture

## 1. Architecture Overview

### 1.1 High-Level System Design

```
1.2 MVP Architecture Principles
🎯 MVP Focus Areas:

Sandbox-Only Operations: All financial operations use MTN's sandbox environment
Manual Verification: Admin manually approves student verifications
Virtual Money Simulation: Internal ledger system for educational purposes
Basic Monitoring: Essential logging and rate limiting
Simple UI: Clean, functional interfaces for core workflows

⚡ Architecture Decisions:

Monolithic FastAPI Backend: Single service for MVP simplicity
PostgreSQL Database: Reliable ACID transactions for financial data
JWT Authentication: Stateless auth with refresh tokens
Direct MTN Proxy: StarterPay proxies requests to MTN sandbox
File Storage in DB: Simple document storage for MVP
┌─────────────────────────────────────────────────────────────────┐
│                    StarterPay System Architecture               │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend Tier │────│   Backend Tier   │────│   Data Tier     │
│                 │    │                  │    │                 │
│ • React App     │    │ • FastAPI Server │    │ • PostgreSQL    │
│ • Admin Panel   │    │ • Authentication │    │ • Redis Cache   │
│ • Mobile Views  │    │ • Business Logic │    │ • File Storage  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                │
                ┌──────────────────────────────────┐
                │        External Services         │
                │                                  │
                │ • MTN MoMo Sandbox API           │
                │ • Email Service (SendGrid)       │
                │ • File Upload Service            │
                │ • Monitoring (Sentry)            │
                └──────────────────────────────────┘
```

### 1.2 Technology Stack Summary

**Frontend:** React 18 + TypeScript + Tailwind CSS + shadcn/ui  
**Backend:** FastAPI + Python 3.11 + SQLAlchemy + Alembic  
**Database:** PostgreSQL 15+ + Redis (Caching & Sessions)  
**Authentication:** JWT   
**Deployment:** Docker + Railway/Render (MVP) → AWS (Production)  
**Monitoring:** Sentry + Structured Logging  

---

## 2. System Components Architecture

### 2.1 Frontend Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                     Frontend Architecture                        │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Public Pages  │    │  Student Portal │    │  Admin Portal   │
│                 │    │                 │    │                 │
│ • Landing       │    │ • Dashboard     │    │ • Verification  │
│ • About         │    │ • API Keys      │    │ • User Mgmt     │
│ • Pricing       │    │ • Transactions  │    │ • Analytics     │
│ • Auth Pages    │    │ • Wallet        │    │ • System Config │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────────────┐
         │              Shared Components & Services                │
         │                                                         │
         │ • Auth Context      • API Client       • Toast System   │
         │ • State Management  • Form Validation  • Error Boundary │
         │ • Route Guards      • Loading States   • Theme System   │
         └─────────────────────────────────────────────────────────┘
```

#### Frontend Folder Structure
```
src/
├── components/           # Reusable UI components
│   ├── ui/              # shadcn/ui components
│   ├── auth/            # Authentication components
│   ├── dashboard/       # Dashboard-specific components
│   └── admin/           # Admin panel components
├── pages/               # Page components
│   ├── public/          # Public pages (landing, about)
│   ├── auth/            # Login, register, verify
│   ├── student/         # Student dashboard pages
│   └── admin/           # Admin panel pages
├── hooks/               # Custom React hooks
├── services/            # API calls and external services
├── stores/              # State management (Zustand)
├── utils/               # Utility functions
├── types/               # TypeScript type definitions
└── constants/           # App constants and config
```

### 2.2 Backend Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                     Backend Architecture                         │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Layer     │    │  Business Logic │    │  Data Access    │
│                 │    │                 │    │                 │
│ • FastAPI Routes│    │ • Services      │    │ • SQLAlchemy    │
│ • Request Valid │    │ • Domain Logic  │    │ • Repository    │
│ • Response Form │    │ • MTN MoMo Proxy│    │ • Models        │
│ • Middleware    │    │ • Auth Service  │    │ • Migrations    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────────────┐
         │                Infrastructure Layer                      │
         │                                                         │
         │ • Database Connection   • Redis Cache   • File Storage  │
         │ • Task Queue (Celery)   • Logging      • Monitoring    │
         │ • Configuration         • Health Checks • Error Handling│
         └─────────────────────────────────────────────────────────┘
```

#### Backend Folder Structure
```
app/
├── api/                 # API routes and endpoints
│   ├── v1/             # API version 1
│   │   ├── auth.py     # Authentication endpoints
│   │   ├── users.py    # User management endpoints
│   │   ├── api_keys.py # API key management
│   │   ├── momo.py     # MTN MoMo proxy endpoints
│   │   ├── admin.py    # Admin endpoints
│   │   └── analytics.py# Analytics endpoints
│   └── dependencies.py # Shared dependencies
├── core/               # Core configuration and utilities
│   ├── config.py       # Application settings
│   ├── security.py     # Security utilities
│   ├── database.py     # Database connection
│   └── exceptions.py   # Custom exceptions
├── models/             # SQLAlchemy models
│   ├── user.py         # User models
│   ├── api_key.py      # API key models
│   ├── transaction.py  # Transaction models
│   └── verification.py # Verification models
├── services/           # Business logic services
│   ├── auth_service.py # Authentication logic
│   ├── user_service.py # User management logic
│   ├── momo_service.py # MTN MoMo integration
│   ├── verification_service.py # KYC logic
│   └── notification_service.py # Notifications
├── schemas/            # Pydantic schemas for validation
├── tasks/              # Background tasks (Celery)
├── utils/              # Utility functions
└── tests/              # Test files
```

---

## 3. Data Flow Architecture

### 3.1 Student Registration & Verification Flow

```
┌─────────────────────────────────────────────────────────────────┐
│                 Student Onboarding Data Flow                    │
└─────────────────────────────────────────────────────────────────┘

Student App         Backend API           Database           Admin Panel
     │                   │                   │                   │
     │ 1. POST /register  │                   │                   │
     ├──────────────────→ │                   │                   │
     │                   │ 2. Create user    │                   │
     │                   ├─────────────────→ │                   │
     │                   │ 3. User created   │                   │
     │                   │ ←─────────────────┤                   │
     │ 4. Registration OK │                   │                   │
     │ ←──────────────────┤                   │                   │
     │                   │                   │                   │
     │ 5. Upload docs     │                   │                   │
     ├──────────────────→ │                   │                   │
     │                   │ 6. Store docs     │                   │
     │                   ├─────────────────→ │                   │
     │                   │ 7. Create verification request        │
     │                   ├─────────────────→ │                   │
     │                   │                   │ 8. Notification   │
     │                   │                   ├─────────────────→ │
     │                   │                   │                   │
     │                   │                   │ 9. Review & approve│
     │                   │                   │ ←─────────────────┤
     │                   │ 10. Update status │                   │
     │                   │ ←─────────────────┤                   │
     │ 11. Approval email │                   │                   │
     │ ←──────────────────┤                   │                   │
```

### 3.2 API Key Generation & Usage Flow

```
┌─────────────────────────────────────────────────────────────────┐
│                   API Key Lifecycle Flow                        │
└─────────────────────────────────────────────────────────────────┘

Student App         Backend API           Database           MTN MoMo API
     │                   │                   │                   │
     │ 1. Generate API Key│                   │                   │
     ├──────────────────→ │                   │                   │
     │                   │ 2. Create key     │                   │
     │                   ├─────────────────→ │                   │
     │                   │ 3. Key stored     │                   │
     │                   │ ←─────────────────┤                   │
     │ 4. Return key      │                   │                   │
     │ ←──────────────────┤                   │                   │
     │                   │                   │                   │
     │ --- Later Usage ---│                   │                   │
     │                   │                   │                   │
     │ 5. API call with key                   │                   │
     ├──────────────────→ │                   │                   │
     │                   │ 6. Validate key   │                   │
     │                   ├─────────────────→ │                   │
     │                   │ 7. Key valid      │                   │
     │                   │ ←─────────────────┤                   │
     │                   │ 8. Check rate limits                  │
     │                   ├─────────────────→ │                   │
     │                   │ 9. Within limits  │                   │
     │                   │ ←─────────────────┤                   │
     │                   │ 10. Proxy to MTN  │                   │
     │                   ├───────────────────┼─────────────────→ │
     │                   │ 11. MTN response  │                   │
     │                   │ ←─────────────────┼───────────────────┤
     │                   │ 12. Log usage     │                   │
     │                   ├─────────────────→ │                   │
     │ 13. Return response│                   │                   │
     │ ←──────────────────┤                   │                   │
```

### 3.3 Transaction Processing Flow (Sandbox)

```
┌─────────────────────────────────────────────────────────────────┐
│              Sandbox Transaction Processing Flow                 │
└─────────────────────────────────────────────────────────────────┘

Student App    StarterPay API    Virtual Wallet    MTN Sandbox    Database
     │              │                 │              │              │
     │ 1. Request payment             │              │              │
     ├─────────────→│                 │              │              │
     │              │ 2. Validate request           │              │
     │              ├───────────────────────────────────────────→│
     │              │ 3. Create transaction         │              │
     │              ├───────────────────────────────────────────→│
     │              │ 4. Call MTN Sandbox           │              │
     │              ├─────────────────────────────→ │              │
     │              │ 5. Sandbox response           │              │
     │              │ ←─────────────────────────────┤              │
     │              │ 6. Update virtual wallet      │              │
     │              ├─────────────→   │              │              │
     │              │ 7. Wallet updated             │              │
     │              │ ←─────────────┘   │              │              │
     │              │ 8. Update transaction status  │              │
     │              ├───────────────────────────────────────────→│
     │              │ 9. Log for analytics          │              │
     │              ├───────────────────────────────────────────→│
     │ 10. Success response           │              │              │
     │ ←─────────────┤                 │              │              │
```

---

## 4. Security Architecture

### 4.1 Authentication & Authorization Flow

```
┌─────────────────────────────────────────────────────────────────┐
│                 Security & Auth Architecture                     │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   JWT Tokens    │    │  Role-Based     │    │  API Key Auth   │
│                 │    │  Access Control │    │                 │
│ • Access Token  │    │ • Student Role  │    │ • Key Validation│
│ • Refresh Token │    │ • Admin Role    │    │ • Rate Limiting │
│ • 15min/7day    │    │ • Super Admin   │    │ • Usage Tracking│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────────────┐
         │                Security Middleware                      │
         │                                                         │
         │ • CORS Policy       • Rate Limiting    • Input Valid   │
         │ • HTTPS Enforcement • SQL Injection    • XSS Protection│
         │ • Security Headers  • Request Logging  • Error Handling│
         └─────────────────────────────────────────────────────────┘
```

#### Security Implementation Details

```python
# JWT Token Structure
{
  "sub": "user_id",
  "email": "<EMAIL>",
  "role": "student",
  "verified": true,
  "environment_access": "sandbox",
  "exp": 1640995200,
  "iat": 1640908800
}

# API Key Structure
{
  "id": "uuid",
  "user_id": "uuid", 
  "key_hash": "hashed_key",
  "environment": "sandbox",
  "rate_limit": 10,
  "usage_quota": 1000,
  "permissions": ["collections", "disbursements"]
}
```

### 4.2 Data Protection Strategy

```
┌─────────────────────────────────────────────────────────────────┐
│                   Data Protection Layers                        │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Data at Rest   │    │ Data in Transit │    │ Data in Memory  │
│                 │    │                 │    │                 │
│ • DB Encryption │    │ • HTTPS/TLS 1.3 │    │ • Secure Sessions│
│ • File Encryption│   │ • Certificate   │    │ • Token Expiry  │
│ • Backup Security│   │   Validation    │    │ • Memory Cleanup│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────────────┐
         │                  Compliance & Audit                     │
         │                                                         │
         │ • GDPR Compliance   • Audit Logging    • Data Retention │
         │ • PII Minimization  • Access Controls  • Regular Reviews│
         └─────────────────────────────────────────────────────────┘
```

---

## 5. Database Architecture

### 5.1 Database Design Philosophy

**Principles:**
- **MVP-First:** Essential tables for core functionality
- **Scalable:** Designed to grow from MVP to production
- **Secure:** Proper constraints and validation
- **Audit-Ready:** History tracking for important changes

### 5.2 Database Schema Overview

```sql
-- MVP Core Tables (Phase 1)
users                    -- Core user management
├── student_profiles     -- Student-specific data
└── admin_profiles       -- Admin-specific data

verification_requests    -- KYC workflow
└── verification_documents -- Document storage

api_keys                -- API access management
├── wallets             -- Virtual/real wallet management
└── transactions        -- Payment processing

api_usage_logs          -- Basic monitoring

-- Phase 2 Enhancement Tables
verification_history     -- Audit trail
transaction_status_history -- Status tracking
api_key_permissions     -- Fine-grained access
rate_limit_violations   -- Advanced monitoring
notifications           -- User communication
support_tickets         -- Help system
system_settings         -- Configuration
```

### 5.3 Data Access Patterns

```python
# Repository Pattern Implementation
class BaseRepository:
    def __init__(self, db: Session, model: DeclarativeMeta):
        self.db = db
        self.model = model
    
    def get_by_id(self, id: UUID) -> Optional[DeclarativeMeta]:
        return self.db.query(self.model).filter(self.model.id == id).first()
    
    def create(self, **kwargs) -> DeclarativeMeta:
        instance = self.model(**kwargs)
        self.db.add(instance)
        self.db.commit()
        self.db.refresh(instance)
        return instance

# Service Layer Pattern
class UserService:
    def __init__(self, user_repo: UserRepository, verification_repo: VerificationRepository):
        self.user_repo = user_repo
        self.verification_repo = verification_repo
    
    async def register_student(self, user_data: StudentRegistrationSchema):
        # Business logic implementation
        pass
```

---

## 6. API Architecture

### 6.1 RESTful API Design

```
┌─────────────────────────────────────────────────────────────────┐
│                      API Architecture                           │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Public APIs   │    │  Authenticated  │    │   Admin APIs    │
│                 │    │     APIs        │    │                 │
│ • /auth/*       │    │ • /users/*      │    │ • /admin/*      │
│ • /docs         │    │ • /api-keys/*   │    │ • /analytics/*  │
│ • /health       │    │ • /wallet/*     │    │ • /system/*     │
│                 │    │ • /momo/*       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────────────┐
         │                 API Gateway Layer                       │
         │                                                         │
         │ • Request Validation  • Response Formatting  • CORS     │
         │ • Authentication      • Rate Limiting        • Logging  │
         │ • Error Handling      • API Versioning       • Docs    │
         └─────────────────────────────────────────────────────────┘
```

### 6.2 API Endpoint Categories

#### Authentication Endpoints
```python
# /api/v1/auth/
POST   /register          # Student registration
POST   /login            # User authentication  
POST   /refresh          # Token refresh
POST   /logout           # User logout
POST   /forgot-password  # Password reset request
POST   /reset-password   # Password reset confirmation
GET    /verify-email     # Email verification
```

#### User Management Endpoints
```python
# /api/v1/users/
GET    /profile          # Get user profile
PUT    /profile          # Update profile
POST   /upload-document  # Upload verification docs
GET    /verification     # Check verification status
GET    /api-keys         # List user's API keys
POST   /api-keys         # Create new API key
DELETE /api-keys/{id}    # Revoke API key
```

#### MTN MoMo Proxy Endpoints (Sandbox)
```python
# /api/v1/momo/
POST   /collections/request-to-pay     # Request payment
GET    /collections/{id}/status        # Check payment status  
POST   /disbursements/transfer         # Send money
GET    /disbursements/{id}/status      # Check transfer status
GET    /account/balance                # Get account balance
```

#### Virtual Wallet Endpoints (Sandbox)
```python
# /api/v1/wallet/
GET    /balance          # Get virtual wallet balance
GET    /transactions     # Get transaction history
POST   /simulate-payment # Simulate payment for testing
```

#### Admin Endpoints
```python
# /api/v1/admin/
GET    /users            # List all users
PUT    /users/{id}/verify # Approve/reject verification
GET    /analytics        # System-wide analytics
GET    /transactions     # All transactions
POST   /users/{id}/promote # Grant production access
```

### 6.3 API Response Format

```python
# Standard Success Response
{
  "success": true,
  "data": {
    # Response data here
  },
  "message": "Operation completed successfully",
  "timestamp": "2025-06-14T10:30:00Z"
}

# Standard Error Response
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "email": ["Invalid email format"],
      "phone": ["Phone number is required"]
    }
  },
  "timestamp": "2025-06-14T10:30:00Z"
}

# Paginated Response
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 150,
    "pages": 8
  },
  "timestamp": "2025-06-14T10:30:00Z"
}
```

---

## 7. Integration Architecture

### 7.1 MTN MoMo Integration Layer

```
┌─────────────────────────────────────────────────────────────────┐
│                 MTN MoMo Integration Architecture                │
└─────────────────────────────────────────────────────────────────┘

StarterPay API    Proxy Layer    MTN MoMo Service    MTN Sandbox API
       │               │               │                    │
       │ 1. Student    │               │                    │
       │    Request    │               │                    │
       ├──────────────→│               │                    │
       │               │ 2. Validate   │                    │
       │               │    & Auth     │                    │
       │               ├──────────────→│                    │
       │               │ 3. Format     │                    │
       │               │    Request    │                    │
       │               │ ←──────────────┤                    │
       │               │ 4. Call MTN   │                    │
       │               ├───────────────────────────────────→│
       │               │ 5. MTN        │                    │
       │               │    Response   │                    │
       │               │ ←───────────────────────────────────┤
       │               │ 6. Process    │                    │
       │               │    Response   │                    │
       │               ├──────────────→│                    │
       │ 7. Format &   │ 7. Update     │                    │
       │    Return     │    Records    │                    │
       │ ←──────────────┤ ←──────────────┤                    │
```

#### MTN MoMo Service Implementation

```python
class MTNMoMoService:
    def __init__(self, config: MTNConfig):
        self.config = config
        self.base_url = config.sandbox_url  # Will switch to production
        self.api_user = config.api_user
        self.api_key = config.api_key
        self.subscription_key = config.subscription_key
    
    async def request_to_pay(self, request: PaymentRequest) -> PaymentResponse:
        """Handle payment collection requests"""
        # 1. Generate access token
        token = await self.get_access_token()
        
        # 2. Format request for MTN
        mtn_request = self.format_payment_request(request)
        
        # 3. Call MTN API
        response = await self.call_mtn_api(
            endpoint="/collection/v1_0/requesttopay",
            method="POST",
            data=mtn_request,
            token=token
        )
        
        # 4. Process response
        return self.process_payment_response(response)
    
    async def check_payment_status(self, reference_id: str) -> PaymentStatus:
        """Check payment status"""
        # Implementation details...
        pass
```

### 7.2 Notification System Architecture

```python
# Event-Driven Notification System
class NotificationService:
    def __init__(self):
        self.email_service = EmailService()
        self.sms_service = SMSService()  # Future
        self.push_service = PushService()  # Future
    
    async def send_notification(self, event: NotificationEvent):
        """Route notifications based on type and user preferences"""
        if event.type == "verification_approved":
            await self.send_verification_approved(event.user, event.data)
        elif event.type == "api_key_created":
            await self.send_api_key_created(event.user, event.data)
        # More event types...
    
    async def send_verification_approved(self, user: User, data: dict):
        """Send verification approval notification"""
        template = "verification_approved"
        context = {
            "user_name": user.full_name,
            "login_url": f"{settings.FRONTEND_URL}/dashboard"
        }
        await self.email_service.send_template_email(
            user.email, template, context
        )
```

---

## 8. Monitoring & Observability

### 8.1 Monitoring Stack

```
┌─────────────────────────────────────────────────────────────────┐
│                   Monitoring Architecture                        │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Application    │    │  Infrastructure │    │   Business      │
│  Monitoring     │    │   Monitoring    │    │  Monitoring     │
│                 │    │                 │    │                 │
│ • Error Tracking│    │ • Server Health │    │ • User Metrics  │
│ • Performance   │    │ • Database      │    │ • API Usage     │
│ • API Metrics   │    │ • Memory/CPU    │    │ • Conversions   │
│ • User Actions  │    │ • Disk Space    │    │ • Revenue       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────────────┐
         │                  Monitoring Tools                       │
         │                                                         │
         │ • Sentry (Errors)     • Railway Metrics  • Custom Logs │
         │ • Uptime Robot        • PostgreSQL Stats • Grafana     │
         │ • LogRocket (Sessions)• Redis Monitoring  • Alerts     │
         └─────────────────────────────────────────────────────────┘
```

### 8.2 Logging Strategy

```python
# Structured Logging Implementation
import structlog
import logging

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

# Usage in application
logger = structlog.get_logger()

# API Request Logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    logger.info(
        "request_started",
        method=request.method,
        url=str(request.url),
        user_agent=request.headers.get("user-agent"),
        ip=request.client.host
    )
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    
    logger.info(
        "request_completed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        duration_ms=int(duration * 1000)
    )
    
    return response
```

---

## 9. Deployment Architecture

### 9.1 MVP Deployment (Railway/Render)

```
┌─