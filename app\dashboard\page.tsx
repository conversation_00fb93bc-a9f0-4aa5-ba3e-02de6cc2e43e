"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON>nt, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function DashboardPage() {
  // Mock data - would come from API in real implementation
  const verificationStatus = "verified" // "verified", "pending", "rejected"
  const apiUsage = { current: 456, limit: 10000, today: 23, dailyLimit: 1000 }
  const recentTransactions = [
    {
      id: "txn_123",
      type: "Collection",
      amount: "+50,000 XAF",
      status: "success",
      date: "2025-06-15",
      time: "2:30 PM",
      phone: "+237612345678",
      customer: "<PERSON>",
    },
    {
      id: "txn_122",
      type: "Collection",
      amount: "+25,000 XAF",
      status: "success",
      date: "2025-06-15",
      time: "1:45 PM",
      phone: "+237698765432",
      customer: "<PERSON>mo",
    },
    {
      id: "txn_121",
      type: "Collection",
      amount: "+75,000 XAF",
      status: "pending",
      date: "2025-06-15",
      time: "12:30 PM",
      phone: "+237623456789",
      customer: "Paul Tabi",
    },
  ]

  const [showFullApiKey, setShowFullApiKey] = useState(false)

  // Production API Key
  const apiKey = "sk_live_1234567890abcdef1234567890abcdef"
  const maskedApiKey = "sk_live_••••••••••••••••••••••••••••1234"

  const successRate = 98.5
  const totalTransactions = 245
  const totalRevenue = 2450000 // XAF
  const monthlyVolume = 15600000 // XAF

  return (
    <div className="container py-6 md:py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Production Dashboard</h1>
        <p className="text-gray-500">Welcome back, John Doe! Monitor your live MTN MoMo integration.</p>
      </div>

      {/* Production Environment Notice */}
      <div className="mb-6 rounded-lg bg-green-50 border border-green-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-green-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-green-800">🚀 Production Environment Active</p>
            <p className="text-sm text-green-700 mt-1">
              You're connected to live MTN MoMo APIs. All transactions are real and will process actual payments.
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="integration">Integration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {/* Account Status Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Account Status</CardTitle>
              </CardHeader>
              <CardContent>
                {verificationStatus === "verified" && (
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="bg-success text-white">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-1 h-3 w-3"
                      >
                        <polyline points="20 6 9 17 4 12" />
                      </svg>
                      Production Ready
                    </Badge>
                  </div>
                )}
                <p className="text-xs text-gray-500 mt-2">MTN MoMo: Connected</p>
              </CardContent>
            </Card>

            {/* API Usage Today Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">API Calls Today</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {apiUsage.today}/{apiUsage.dailyLimit}
                </div>
                <Progress value={(apiUsage.today / apiUsage.dailyLimit) * 100} className="mt-2" />
                <p className="text-xs text-gray-500 mt-2">
                  {Math.round((apiUsage.today / apiUsage.dailyLimit) * 100)}% of daily limit
                </p>
              </CardContent>
            </Card>

            {/* Monthly Usage Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">This Month</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {apiUsage.current}/{apiUsage.limit}
                </div>
                <Progress value={(apiUsage.current / apiUsage.limit) * 100} className="mt-2" />
                <p className="text-xs text-gray-500 mt-2">
                  {Math.round((apiUsage.current / apiUsage.limit) * 100)}% of monthly limit
                </p>
              </CardContent>
            </Card>

            {/* Revenue Card */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalRevenue.toLocaleString()} XAF</div>
                <p className="text-xs text-gray-500 mt-2">Total processed this month</p>
              </CardContent>
            </Card>
          </div>

          {/* Business Metrics Cards */}
          <div className="grid gap-6 md:grid-cols-3 mt-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{totalTransactions}</div>
                <p className="text-xs text-gray-500">All time</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-success">{successRate}%</div>
                <p className="text-xs text-gray-500">Last 30 days</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Transaction Volume</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{(monthlyVolume / 1000000).toFixed(1)}M XAF</div>
                <p className="text-xs text-gray-500">This month</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>Latest live payment transactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTransactions.slice(0, 3).map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          transaction.status === "success"
                            ? "bg-success"
                            : transaction.status === "pending"
                              ? "bg-warning"
                              : "bg-destructive"
                        }`}
                      />
                      <div>
                        <p className="text-sm font-medium">{transaction.customer}</p>
                        <p className="text-xs text-gray-500">
                          {transaction.date} at {transaction.time}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{transaction.amount}</p>
                      <p className="text-xs text-gray-500">{transaction.phone}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" asChild className="w-full">
                <Link href="/transactions">View All Transactions</Link>
              </Button>
            </CardFooter>
          </Card>

          {/* Quick Actions */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Manage your production integration</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button asChild className="h-auto flex-col py-4">
                  <Link href="/api-keys">
                    <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586l4.293-4.293A6 6 0 0115 7z"
                      />
                    </svg>
                    Manage API Keys
                  </Link>
                </Button>
                <Button variant="outline" asChild className="h-auto flex-col py-4">
                  <Link href="/docs">
                    <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414A6 6 0 0115 7z"
                      />
                    </svg>
                    View Documentation
                  </Link>
                </Button>
                <Button variant="outline" className="h-auto flex-col py-4">
                  <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  Test Connection
                </Button>
                <Button variant="outline" asChild className="h-auto flex-col py-4">
                  <Link href="/sandbox">
                    <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                      />
                    </svg>
                    Try Sandbox
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Production API Key Management Card */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Production API Key</CardTitle>
              <CardDescription>Your live API key for MTN MoMo production endpoints</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <code className="flex-1 p-2 bg-gray-100 rounded text-sm font-mono">
                    {showFullApiKey ? apiKey : maskedApiKey}
                  </code>
                  <Button variant="outline" size="sm" onClick={() => setShowFullApiKey(!showFullApiKey)}>
                    {showFullApiKey ? "Hide" : "Show"}
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => navigator.clipboard.writeText(apiKey)}>
                    Copy
                  </Button>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-red-800 mb-2">🔒 Production Security Guidelines</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• This key processes REAL money transactions</li>
                    <li>• Never expose in client-side code or public repositories</li>
                    <li>• Use server-side environment variables only</li>
                    <li>• Monitor usage and regenerate if compromised</li>
                    <li>• Rate limit: 1000 requests per minute</li>
                  </ul>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline">Regenerate Key</Button>
                  <Button variant="outline">View Usage Logs</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <div className="grid gap-6">
            {/* Business Analytics */}
            <Card>
              <CardHeader>
                <CardTitle>Business Analytics</CardTitle>
                <CardDescription>Detailed breakdown of your payment processing performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="rounded-lg border bg-card p-4">
                    <div className="text-sm font-medium text-gray-500">Total Revenue</div>
                    <div className="text-2xl font-bold">{(totalRevenue / 1000000).toFixed(1)}M XAF</div>
                    <div className="text-xs text-gray-400">All time</div>
                  </div>
                  <div className="rounded-lg border bg-card p-4">
                    <div className="text-sm font-medium text-gray-500">Avg Transaction</div>
                    <div className="text-2xl font-bold">
                      {Math.round(totalRevenue / totalTransactions).toLocaleString()} XAF
                    </div>
                    <div className="text-xs text-gray-400">Per transaction</div>
                  </div>
                  <div className="rounded-lg border bg-card p-4">
                    <div className="text-sm font-medium text-gray-500">Success Rate</div>
                    <div className="text-2xl font-bold text-success">{successRate}%</div>
                    <div className="text-xs text-gray-400">Last 30 days</div>
                  </div>
                  <div className="rounded-lg border bg-card p-4">
                    <div className="text-sm font-medium text-gray-500">Avg Response Time</div>
                    <div className="text-2xl font-bold">156ms</div>
                    <div className="text-xs text-gray-400">API performance</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Production Environment Status */}
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="text-green-800">🚀 Production Environment</CardTitle>
                <CardDescription className="text-green-600">
                  You're operating in the live MTN MoMo environment
                </CardDescription>
              </CardHeader>
              <CardContent className="text-green-700">
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>All transactions process real money through MTN MoMo</li>
                  <li>Connected to live MTN payment infrastructure</li>
                  <li>Full compliance with MTN security and regulatory requirements</li>
                  <li>24/7 monitoring and support for production issues</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="integration">
          <Card>
            <CardHeader>
              <CardTitle>Production Integration Guide</CardTitle>
              <CardDescription>Best practices for your live MTN MoMo integration</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-xl font-bold">Production Checklist</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded-full bg-success flex items-center justify-center">
                      <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                        <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z" />
                      </svg>
                    </div>
                    <span className="text-sm">KYC verification completed</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded-full bg-success flex items-center justify-center">
                      <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                        <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z" />
                      </svg>
                    </div>
                    <span className="text-sm">Production API key generated</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded-full bg-success flex items-center justify-center">
                      <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                        <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z" />
                      </svg>
                    </div>
                    <span className="text-sm">MTN MoMo connection established</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded-full bg-success flex items-center justify-center">
                      <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                        <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z" />
                      </svg>
                    </div>
                    <span className="text-sm">Webhook endpoints configured</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="text-lg font-medium">Production API Endpoints</h4>
                <div className="rounded-md bg-gray-900 p-4">
                  <pre className="text-sm text-white">
                    <code>{`// Production Base URL
const API_BASE = 'https://api.starterpay.cm/v1'

// Request Payment
POST /payments/request
{
  "amount": 10000,
  "currency": "XAF",
  "phoneNumber": "237612345678",
  "externalId": "order_123",
  "description": "Payment for Order #123"
}

// Check Payment Status
GET /payments/{paymentId}/status`}</code>
                  </pre>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-yellow-800 mb-2">⚠️ Production Environment Notice</h4>
                <p className="text-sm text-yellow-700">
                  You are now operating in the production environment. All transactions will process real money through
                  MTN MoMo. Ensure your application handles errors gracefully and implements proper security measures.
                </p>
              </div>

              <Button className="w-full bg-gradient-to-r from-primary-600 to-primary-700">
                View Complete Production Documentation
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
