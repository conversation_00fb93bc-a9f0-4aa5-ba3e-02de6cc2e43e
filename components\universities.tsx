import Image from "next/image"

export function Universities() {
  const universities = [
    "University of Yaoundé",
    "University of Douala",
    "University of Buea",
    "University of Bamenda",
    "University of Dschang",
    "University of Maroua",
    "Catholic University of Cameroon",
    "American Institute of Cameroon",
  ]

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-full bg-primary-100 px-3 py-1 text-sm font-medium text-primary-700">
              Partners
            </div>
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Supported Universities</h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed">
              We partner with leading institutions across Cameroon to support student innovation.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-2 gap-8 md:grid-cols-4 mt-12">
          {universities.map((university, index) => (
            <div
              key={index}
              className="flex flex-col items-center justify-center p-4 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all"
            >
              <Image
                src={`/placeholder.svg?height=80&width=120&text=${university.replace(/ /g, "+")}`}
                alt={university}
                width={120}
                height={80}
                className="object-contain mb-3"
              />
              <p className="text-xs font-medium text-center">{university}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
