import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function DocumentationPage() {
  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Hero Section */}
      <div className="mb-12">
        <h1 className="text-4xl font-bold tracking-tighter mb-4">StarterPay Documentation</h1>
        <p className="text-xl text-gray-600 mb-8">
          Everything you need to integrate MTN Mobile Money into your applications. From getting started to advanced
          implementations.
        </p>

        {/* Sandbox CTA */}
        <Card className="mb-8 border-blue-200 bg-blue-50">
          <CardHeader>
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                🧪 Try It Out
              </Badge>
              <CardTitle className="text-blue-900">Sandbox Environment</CardTitle>
            </div>
            <CardDescription className="text-blue-700">
              Test MTN MoMo APIs without any risk. Perfect for learning and development.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button asChild className="bg-blue-600 hover:bg-blue-700">
                <Link href="/sandbox">Try Sandbox Environment</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="#sandbox-guide">View Sandbox Guide</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Start */}
      <section className="mb-12">
        <h2 className="text-3xl font-bold mb-6">Quick Start</h2>
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="rounded-full bg-green-100 p-2 text-green-700">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3" />
                  </svg>
                </div>
                Getting Started
              </CardTitle>
              <CardDescription>Learn the basics of MTN MoMo integration and set up your first payment.</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Account Setup & KYC Process</li>
                <li>• API Key Generation</li>
                <li>• Authentication Methods</li>
                <li>• Your First Payment</li>
              </ul>
              <Button variant="outline" className="mt-4 w-full" asChild>
                <Link href="/docs/quick-start">View Getting Started</Link>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="rounded-full bg-blue-100 p-2 text-blue-700">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
                    <rect width="8" height="4" x="8" y="2" rx="1" ry="1" />
                  </svg>
                </div>
                API Reference
              </CardTitle>
              <CardDescription>Complete API documentation with examples and response formats.</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Payment Endpoints</li>
                <li>• Webhook Configuration</li>
                <li>• Error Handling</li>
                <li>• Rate Limits & Security</li>
              </ul>
              <Button variant="outline" className="mt-4 w-full" asChild>
                <Link href="/docs/api/payments">View API Reference</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Sandbox Guide */}
      <section id="sandbox-guide" className="mb-12">
        <h2 className="text-3xl font-bold mb-6">Sandbox Environment Guide</h2>
        <div className="grid gap-8 md:grid-cols-2">
          <div>
            <h3 className="text-xl font-bold mb-4">What is the Sandbox?</h3>
            <p className="text-gray-600 mb-4">
              Our sandbox environment is a safe testing space where you can experiment with MTN MoMo APIs without using
              real money or affecting live transactions.
            </p>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• No real money involved</li>
              <li>• Unlimited virtual funds</li>
              <li>• Test all API endpoints</li>
              <li>• Simulate different scenarios</li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-4">How to Access</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="rounded-full bg-blue-600 text-white w-6 h-6 flex items-center justify-center text-sm font-bold">
                  1
                </div>
                <p className="text-sm text-gray-600">Sign up for a StarterPay account</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="rounded-full bg-blue-600 text-white w-6 h-6 flex items-center justify-center text-sm font-bold">
                  2
                </div>
                <p className="text-sm text-gray-600">Login and choose "Sandbox Environment"</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="rounded-full bg-blue-600 text-white w-6 h-6 flex items-center justify-center text-sm font-bold">
                  3
                </div>
                <p className="text-sm text-gray-600">Start testing with virtual funds</p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
            <Link href="/sandbox">Access Sandbox Environment</Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
