"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"

export default function TransactionsPage() {
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Mock data - would come from API in real implementation
  const transactions = [
    {
      id: "tx_001",
      type: "Collection",
      amount: "5,000 XAF",
      status: "success",
      date: "2025-06-10",
      phoneNumber: "237612345678",
      description: "Payment for Order #123",
      requestData: {
        amount: 5000,
        currency: "XAF",
        phoneNumber: "237612345678",
        externalId: "order_123",
        description: "Payment for Order #123",
      },
      responseData: {
        status: "SUCCESSFUL",
        transactionId: "momo_12345",
        externalId: "order_123",
        amount: 5000,
        currency: "XAF",
        payer: {
          phoneNumber: "237612345678",
          name: "John Doe",
        },
        createdAt: "2025-06-10T10:30:00Z",
        updatedAt: "2025-06-10T10:31:05Z",
      },
    },
    {
      id: "tx_002",
      type: "Collection",
      amount: "2,500 XAF",
      status: "failed",
      date: "2025-06-09",
      phoneNumber: "************",
      description: "Payment for Order #124",
      requestData: {
        amount: 2500,
        currency: "XAF",
        phoneNumber: "************",
        externalId: "order_124",
        description: "Payment for Order #124",
      },
      responseData: {
        status: "FAILED",
        error: "PAYER_NOT_FOUND",
        message: "The specified payer account was not found",
        externalId: "order_124",
        createdAt: "2025-06-09T15:20:00Z",
        updatedAt: "2025-06-09T15:20:10Z",
      },
    },
    {
      id: "tx_003",
      type: "Collection",
      amount: "8,000 XAF",
      status: "pending",
      date: "2025-06-08",
      phoneNumber: "************",
      description: "Payment for Order #125",
      requestData: {
        amount: 8000,
        currency: "XAF",
        phoneNumber: "************",
        externalId: "order_125",
        description: "Payment for Order #125",
      },
      responseData: {
        status: "PENDING",
        externalId: "order_125",
        amount: 8000,
        currency: "XAF",
        createdAt: "2025-06-08T09:15:00Z",
        updatedAt: "2025-06-08T09:15:00Z",
      },
    },
  ]

  const handleViewTransaction = (transaction: any) => {
    setSelectedTransaction(transaction)
    setIsDialogOpen(true)
  }

  return (
    <div className="container py-6 md:py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Transactions</h1>
        <p className="text-gray-500">View and manage your payment transactions</p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Transaction Filters</CardTitle>
          <CardDescription>Filter your transaction history</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <div>
              <label htmlFor="search" className="text-sm font-medium">
                Search
              </label>
              <Input id="search" placeholder="Transaction ID or description" className="mt-1" />
            </div>
            <div>
              <label htmlFor="status" className="text-sm font-medium">
                Status
              </label>
              <Select>
                <SelectTrigger id="status" className="mt-1">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="success">Success</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label htmlFor="type" className="text-sm font-medium">
                Type
              </label>
              <Select>
                <SelectTrigger id="type" className="mt-1">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="collection">Collection</SelectItem>
                  <SelectItem value="disbursement">Disbursement</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label htmlFor="date" className="text-sm font-medium">
                Date Range
              </label>
              <Input id="date" type="date" className="mt-1" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Transaction History</CardTitle>
          <CardDescription>Your recent payment transactions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell className="font-mono text-sm">{transaction.id}</TableCell>
                    <TableCell>{transaction.type}</TableCell>
                    <TableCell>{transaction.amount}</TableCell>
                    <TableCell>
                      {transaction.status === "success" && (
                        <Badge variant="outline" className="bg-success text-white">
                          Success
                        </Badge>
                      )}
                      {transaction.status === "failed" && (
                        <Badge variant="outline" className="bg-destructive text-white">
                          Failed
                        </Badge>
                      )}
                      {transaction.status === "pending" && (
                        <Badge variant="outline" className="bg-warning text-white">
                          Pending
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>{transaction.date}</TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm" onClick={() => handleViewTransaction(transaction)}>
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-gray-500">Showing 1-3 of 3 transactions</div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" disabled>
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        {selectedTransaction && (
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Transaction Details</DialogTitle>
              <DialogDescription>
                Transaction ID: <span className="font-mono">{selectedTransaction.id}</span>
              </DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <h3 className="text-lg font-medium mb-2">Transaction Information</h3>
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Type</div>
                    <div className="text-sm">{selectedTransaction.type}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Amount</div>
                    <div className="text-sm">{selectedTransaction.amount}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Status</div>
                    <div className="text-sm">
                      {selectedTransaction.status === "success" && (
                        <Badge variant="outline" className="bg-success text-white">
                          Success
                        </Badge>
                      )}
                      {selectedTransaction.status === "failed" && (
                        <Badge variant="outline" className="bg-destructive text-white">
                          Failed
                        </Badge>
                      )}
                      {selectedTransaction.status === "pending" && (
                        <Badge variant="outline" className="bg-warning text-white">
                          Pending
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Date</div>
                    <div className="text-sm">{selectedTransaction.date}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Phone Number</div>
                    <div className="text-sm">{selectedTransaction.phoneNumber}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Description</div>
                    <div className="text-sm">{selectedTransaction.description}</div>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Technical Details</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-1">Request Data</h4>
                    <div className="rounded-md bg-gray-900 p-3 max-h-40 overflow-auto">
                      <pre className="text-xs text-white">
                        <code>{JSON.stringify(selectedTransaction.requestData, null, 2)}</code>
                      </pre>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium mb-1">Response Data</h4>
                    <div className="rounded-md bg-gray-900 p-3 max-h-40 overflow-auto">
                      <pre className="text-xs text-white">
                        <code>{JSON.stringify(selectedTransaction.responseData, null, 2)}</code>
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-4">
              {selectedTransaction.status === "failed" && <Button variant="outline">Retry Transaction</Button>}
              <Button>Close</Button>
            </div>
          </DialogContent>
        )}
      </Dialog>
    </div>
  )
}
