import { NextRequest, NextResponse } from 'next/server'
import { withStudentAuth } from '@/lib/api-auth'
import { SandboxError } from '@/lib/sandbox-service'

/**
 * GET /api/sandbox/transactions
 * List user's sandbox transactions
 */
export const GET = withStudentAuth(async (request, auth) => {
  try {
    const { prisma } = await import('@/lib/prisma')
    
    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    
    const transactions = await prisma.sandboxTransaction.findMany({
      where: { userId: auth.user.id },
      orderBy: { createdAt: 'desc' },
      take: Math.min(limit, 100), // Max 100 transactions per request
      skip: offset,
      select: {
        id: true,
        externalId: true,
        amount: true,
        currency: true,
        type: true,
        status: true,
        mtnTransactionId: true,
        mtnReferenceId: true,
        payerPhone: true,
        payerMessage: true,
        payeeNote: true,
        failureReason: true,
        createdAt: true,
        updatedAt: true,
        completedAt: true
      }
    })

    // Convert Decimal to number for JSON serialization
    const formattedTransactions = transactions.map(transaction => ({
      ...transaction,
      amount: Number(transaction.amount)
    }))

    return NextResponse.json({
      success: true,
      data: formattedTransactions,
      pagination: {
        limit,
        offset,
        total: formattedTransactions.length
      }
    })

  } catch (error) {
    console.error('List transactions error:', error)

    if (error instanceof SandboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code
        },
        { status: error.statusCode }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
})
