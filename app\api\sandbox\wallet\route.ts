import { NextRequest, NextResponse } from 'next/server'
import { withStudentAuth } from '@/lib/api-auth'
import { SandboxService, SandboxError } from '@/lib/sandbox-service'



/**
 * GET /api/sandbox/wallet
 * Get user's sandbox wallet details
 */
export const GET = withStudentAuth(async (request, auth) => {
  try {
    const sandboxService = new SandboxService()
    const wallet = await sandboxService.getWallet(auth.user.id)

    if (!wallet) {
      return NextResponse.json(
        { error: 'Wallet not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: wallet.id,
        balance: Number(wallet.balance),
        currency: wallet.currency,
        createdAt: wallet.createdAt,
        recentTransactions: wallet.transactions.map((tx: { id: string; externalId: string; amount: number; currency: string; status: string; type: string; createdAt: Date; completedAt?: Date }) => ({
          id: tx.id,
          externalId: tx.externalId,
          amount: Number(tx.amount),
          currency: tx.currency,
          status: tx.status,
          type: tx.type,
          createdAt: tx.createdAt,
          completedAt: tx.completedAt
        }))
      }
    })

  } catch (error) {
    console.error('Get wallet error:', error)

    if (error instanceof SandboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code
        },
        { status: error.statusCode }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
})


