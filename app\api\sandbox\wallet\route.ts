import { NextRequest, NextResponse } from 'next/server'
import { withStudentAuth } from '@/lib/api-auth'
import { SandboxService, SandboxError } from '@/lib/sandbox-service'
import { z } from 'zod'

const addFundsSchema = z.object({
  amount: z.number().positive().max(1000000) // Max 1M XAF
})

/**
 * GET /api/sandbox/wallet
 * Get user's sandbox wallet details
 */
export const GET = withStudentAuth(async (request, auth) => {
  try {
    const sandboxService = new SandboxService()
    const wallet = await sandboxService.getWallet(auth.user.id)

    if (!wallet) {
      return NextResponse.json(
        { error: 'Wallet not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: wallet.id,
        balance: Number(wallet.balance),
        currency: wallet.currency,
        createdAt: wallet.createdAt,
        recentTransactions: wallet.transactions.map(tx => ({
          id: tx.id,
          externalId: tx.externalId,
          amount: Number(tx.amount),
          currency: tx.currency,
          status: tx.status,
          type: tx.type,
          createdAt: tx.createdAt,
          completedAt: tx.completedAt
        }))
      }
    })

  } catch (error) {
    console.error('Get wallet error:', error)

    if (error instanceof SandboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code
        },
        { status: error.statusCode }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
})

/**
 * POST /api/sandbox/wallet/add-funds
 * Add virtual funds to sandbox wallet (for testing)
 */
export const POST = withStudentAuth(async (request, auth) => {
  try {
    const body = await request.json()
    const { amount } = addFundsSchema.parse(body)

    const sandboxService = new SandboxService()
    await sandboxService.addFunds(auth.user.id, amount)

    // Get updated wallet
    const wallet = await sandboxService.getWallet(auth.user.id)

    return NextResponse.json({
      success: true,
      message: `Added ${amount} XAF to your sandbox wallet`,
      data: {
        newBalance: Number(wallet?.balance || 0),
        currency: wallet?.currency || 'XAF'
      }
    })

  } catch (error) {
    console.error('Add funds error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation error',
          details: error.errors
        },
        { status: 400 }
      )
    }

    if (error instanceof SandboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code
        },
        { status: error.statusCode }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
})
