"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Copy, Check, Plus } from "lucide-react"

interface ApiKeyData {
  id: string
  keyName: string
  environment: string
  isActive: boolean
  usageCount: number
  rateLimit: number
  createdAt: string
  lastUsedAt: string | null
}

export default function ApiAccessPage() {
  const [apiKeys, setApiKeys] = useState<ApiKeyData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showFullApiKey, setShowFullApiKey] = useState<{[key: string]: boolean}>({})
  const [copied, setCopied] = useState<{[key: string]: boolean}>({})
  const [isCreating, setIsCreating] = useState(false)
  const [newKeyName, setNewKeyName] = useState("")
  const [newlyCreatedKey, setNewlyCreatedKey] = useState<string | null>(null)

  // Fetch API keys
  useEffect(() => {
    fetchApiKeys()
  }, [])

  const fetchApiKeys = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/sandbox/api-keys')
      if (!response.ok) {
        throw new Error('Failed to fetch API keys')
      }

      const result = await response.json()
      if (result.success) {
        setApiKeys(result.data)
      } else {
        throw new Error(result.error || 'Failed to fetch API keys')
      }
    } catch (err) {
      console.error('Error fetching API keys:', err)
      setError(err instanceof Error ? err.message : 'Failed to load API keys')
    } finally {
      setIsLoading(false)
    }
  }

  const createApiKey = async () => {
    if (!newKeyName.trim()) return

    try {
      setIsCreating(true)

      const response = await fetch('/api/sandbox/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keyName: newKeyName.trim()
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create API key')
      }

      const result = await response.json()
      if (result.success) {
        setNewlyCreatedKey(result.data.apiKey) // Store the actual key value
        setNewKeyName("")
        await fetchApiKeys() // Refresh the list
      } else {
        throw new Error(result.error || 'Failed to create API key')
      }
    } catch (err) {
      console.error('Error creating API key:', err)
      setError(err instanceof Error ? err.message : 'Failed to create API key')
    } finally {
      setIsCreating(false)
    }
  }

  const copyToClipboard = (text: string, keyId: string) => {
    navigator.clipboard.writeText(text)
    setCopied(prev => ({ ...prev, [keyId]: true }))
    setTimeout(() => {
      setCopied(prev => ({ ...prev, [keyId]: false }))
    }, 2000)
  }

  const maskApiKey = (key: string) => {
    if (key.length <= 8) return key
    return key.substring(0, 20) + '••••••••••••••••••••••••••••' + key.substring(key.length - 4)
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading API keys...</p>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">API Access</h1>
            <p className="text-gray-500">Manage your Sandbox API Keys and credentials</p>
          </div>
          <Button onClick={() => setIsCreating(true)} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="mr-2 h-4 w-4" />
            Create API Key
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Newly Created Key Alert */}
      {newlyCreatedKey && (
        <Alert className="mb-6 bg-green-50 border-green-200">
          <AlertTitle className="text-green-800">API Key Created Successfully!</AlertTitle>
          <AlertDescription className="text-green-700">
            <p className="mb-2">Your new API key has been created. Copy it now - it won't be shown again:</p>
            <div className="flex items-center space-x-2 mt-2">
              <code className="flex-1 p-2 bg-green-100 rounded text-sm font-mono overflow-auto">
                {newlyCreatedKey}
              </code>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(newlyCreatedKey, 'new-key')}
                className="min-w-[70px]"
              >
                {copied['new-key'] ? (
                  <>
                    <Check className="mr-1 h-4 w-4" /> Copied
                  </>
                ) : (
                  <>
                    <Copy className="mr-1 h-4 w-4" /> Copy
                  </>
                )}
              </Button>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setNewlyCreatedKey(null)}
              className="mt-2"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Sandbox Environment Notice */}
      <div className="mb-6 rounded-lg bg-blue-50 border border-blue-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-blue-800">🧪 Sandbox API Keys</p>
            <p className="text-sm text-blue-700 mt-1">
              These keys only work in the sandbox environment. No real money will be transacted.
            </p>
          </div>
        </div>
      </div>

      {/* API Keys List */}
      {apiKeys.length === 0 ? (
        <Card className="mb-6">
          <CardContent className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7 6h-2m-6 0H9a6 6 0 017-6V7a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No API Keys</h3>
            <p className="text-gray-500 mb-4">Create your first API key to start testing MTN MoMo integration</p>
            <Button onClick={() => setIsCreating(true)} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="mr-2 h-4 w-4" />
              Create API Key
            </Button>
          </CardContent>
        </Card>
      ) : (
        apiKeys.map((apiKey) => (
          <Card key={apiKey.id} className="mb-6">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{apiKey.keyName}</CardTitle>
                  <CardDescription>Use this key for testing MTN MoMo integration</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className={apiKey.isActive ? "bg-green-100 text-green-700 border-green-300" : "bg-gray-100 text-gray-700 border-gray-300"}>
                    {apiKey.isActive ? 'ACTIVE' : 'INACTIVE'}
                  </Badge>
                  <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                    SANDBOX
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                {/* API Key Display */}
                <div>
                  <label className="text-sm font-medium mb-1 block">API Key</label>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 p-3 bg-gray-100 rounded-md text-sm font-mono overflow-auto">
                      {showFullApiKey[apiKey.id] ? `sk_sandbox_${apiKey.id}` : maskApiKey(`sk_sandbox_${apiKey.id}`)}
                    </code>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowFullApiKey(prev => ({ ...prev, [apiKey.id]: !prev[apiKey.id] }))}
                      className="whitespace-nowrap"
                    >
                      {showFullApiKey[apiKey.id] ? "Hide" : "Show"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(`sk_sandbox_${apiKey.id}`, apiKey.id)}
                      className="min-w-[70px] flex items-center justify-center"
                    >
                      {copied[apiKey.id] ? (
                        <>
                          <Check className="mr-1 h-4 w-4" /> Copied
                        </>
                      ) : (
                        <>
                          <Copy className="mr-1 h-4 w-4" /> Copy
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {/* Key Metadata */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Key Name</p>
                    <p className="text-sm text-gray-500">{apiKey.keyName}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Environment</p>
                    <div className="text-sm text-gray-500">
                      <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                        {apiKey.environment.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Created At</p>
                    <p className="text-sm text-gray-500">{new Date(apiKey.createdAt).toLocaleDateString()}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Usage Count</p>
                    <p className="text-sm text-gray-500">{apiKey.usageCount.toLocaleString()} requests</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Last Used</p>
                    <p className="text-sm text-gray-500">
                      {apiKey.lastUsedAt ? new Date(apiKey.lastUsedAt).toLocaleDateString() : 'Never'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Rate Limit</p>
                    <p className="text-sm text-gray-500">{apiKey.rateLimit} requests/hour</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      )}

      {/* Security Guidelines */}
      <Alert variant="destructive" className="bg-red-50 border-red-200 text-red-800 mb-6">
        <AlertTitle className="text-red-800">API Key Security Guidelines</AlertTitle>
        <AlertDescription className="text-red-700">
          <ul className="list-disc pl-5 space-y-1 mt-2">
            <li>These are test keys for the sandbox environment only</li>
            <li>Never expose API keys in client-side code or public repositories</li>
            <li>Use environment variables to store API keys in your code</li>
            <li>Keep your API keys confidential and secure</li>
          </ul>
        </AlertDescription>
      </Alert>

      {/* API Base URLs */}
      <Card>
        <CardHeader>
          <CardTitle>API Base URLs</CardTitle>
          <CardDescription>Endpoints for your Sandbox integration</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="text-sm font-medium mb-1 block">Base URL</label>
            <div className="flex items-center space-x-2">
              <code className="flex-1 p-3 bg-gray-100 rounded-md text-sm font-mono">
                https://sandbox-api.starterpay.cm/v1
              </code>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard("https://sandbox-api.starterpay.cm/v1", "base-url")}
              >
                {copied['base-url'] ? (
                  <>
                    <Check className="mr-1 h-4 w-4" /> Copied
                  </>
                ) : (
                  <>
                    <Copy className="mr-1 h-4 w-4" /> Copy
                  </>
                )}
              </Button>
            </div>
          </div>

          <div className="space-y-3 mt-4">
            <div>
              <p className="text-sm font-medium mb-1">Collections API</p>
              <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono">
                POST /collections/request-to-pay
              </code>
              <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono mt-2">
                GET /collections/{"{transactionId}"}/status
              </code>
            </div>

            <div className="mt-2">
              <p className="text-sm font-medium mb-1">Disbursements API</p>
              <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono">
                POST /disbursements/transfer
              </code>
              <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono mt-2">
                GET /disbursements/{"{transactionId}"}/status
              </code>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
            <div className="flex items-start gap-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-blue-600 mt-0.5"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M12 16v-4" />
                <path d="M12 8h.01" />
              </svg>
              <div>
                <p className="text-sm font-medium text-blue-800">Need more help?</p>
                <p className="text-xs text-blue-700 mt-1">
                  Visit the API Testing page to try these endpoints with a user-friendly interface.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Create API Key Dialog */}
      {isCreating && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-medium mb-4">Create New API Key</h3>
            <p className="text-sm text-gray-600 mb-4">
              Give your API key a descriptive name to help you identify it later.
            </p>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Key Name</label>
                <Input
                  type="text"
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                  placeholder="e.g., My App Development Key"
                  maxLength={50}
                />
                <p className="text-xs text-gray-500 mt-1">Maximum 50 characters</p>
              </div>
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => {
                  setIsCreating(false)
                  setNewKeyName("")
                }}>
                  Cancel
                </Button>
                <Button
                  onClick={createApiKey}
                  disabled={!newKeyName.trim() || isCreating}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isCreating ? 'Creating...' : 'Create Key'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}