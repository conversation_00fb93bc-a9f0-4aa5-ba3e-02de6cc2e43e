'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestLoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const testSupabaseConnection = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/test-supabase')
      const data = await response.json()
      setResult({ type: 'connection', data })
    } catch (error) {
      setResult({ type: 'error', data: { error: error instanceof Error ? error.message : 'Unknown error' } })
    } finally {
      setLoading(false)
    }
  }

  const testClientLogin = async () => {
    if (!email || !password) {
      setResult({ type: 'error', data: { error: 'Please enter email and password' } })
      return
    }

    setLoading(true)
    setResult(null)
    
    try {
      const { createClient } = await import('@/lib/supabase/client')
      const supabase = createClient()
      
      console.log('Attempting client-side login...')
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (error) {
        setResult({ type: 'error', data: { error: error.message } })
      } else {
        setResult({ type: 'success', data: { user: data.user?.id, email: data.user?.email } })
      }
    } catch (error) {
      setResult({ type: 'error', data: { error: error instanceof Error ? error.message : 'Unknown error' } })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container max-w-md mx-auto mt-10">
      <Card>
        <CardHeader>
          <CardTitle>Supabase Connection Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testSupabaseConnection} disabled={loading} className="w-full">
            {loading ? 'Testing...' : 'Test Supabase Connection'}
          </Button>
          
          <div className="space-y-2">
            <Input
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <Input
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
            <Button onClick={testClientLogin} disabled={loading} className="w-full">
              {loading ? 'Testing...' : 'Test Client Login'}
            </Button>
          </div>
          
          {result && (
            <div className={`p-4 rounded ${result.type === 'error' ? 'bg-red-100' : 'bg-green-100'}`}>
              <h3 className="font-bold">{result.type === 'error' ? 'Error' : 'Success'}</h3>
              <pre className="text-sm mt-2 overflow-auto">
                {JSON.stringify(result.data, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
