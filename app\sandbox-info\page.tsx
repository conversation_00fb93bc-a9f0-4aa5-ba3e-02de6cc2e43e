import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"

export default function SandboxLandingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-16 md:py-24 bg-gradient-to-b from-blue-50 to-white">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center">
              <div className="inline-block rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-700 mb-4">
                🧪 Sandbox Environment
              </div>
              <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl">
                Learn & Test <span className="text-blue-600">MTN MoMo APIs</span>
              </h1>
              <p className="mt-6 text-lg text-gray-600 md:text-xl">
                Practice with our sandbox environment. No real money, no risk - just pure learning and experimentation
                with MTN Mobile Money APIs.
              </p>
              <div className="mt-8 flex flex-col gap-4 sm:flex-row sm:justify-center">
                <Button size="lg" asChild className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-full px-8">
                  <Link href="/login/sandbox">Enter Sandbox</Link>
                </Button>
                <Button size="lg" variant="outline" asChild className="rounded-full px-8">
                  <Link href="/docs">View Documentation</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Perfect for Learning</h2>
              <p className="mt-4 text-gray-600 md:text-lg">
                Our sandbox provides a safe environment to learn MTN MoMo integration without any risks.
              </p>
            </div>
            <div className="grid gap-8 md:grid-cols-3">
              <Card className="border-none shadow-lg">
                <CardHeader>
                  <div className="rounded-full bg-blue-100 p-3 text-blue-700 w-fit mx-auto">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-6 w-6"
                    >
                      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10" />
                      <path d="M9 12l2 2 4-4" />
                    </svg>
                  </div>
                  <CardTitle className="text-center">Safe Testing</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    All transactions are simulated. No real money is involved, making it perfect for learning and
                    testing your integration.
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="border-none shadow-lg">
                <CardHeader>
                  <div className="rounded-full bg-blue-100 p-3 text-blue-700 w-fit mx-auto">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-6 w-6"
                    >
                      <path d="M12 2v20" />
                      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                    </svg>
                  </div>
                  <CardTitle className="text-center">Virtual Wallet</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    Get virtual funds to test payment flows. Add test money to your sandbox wallet and experiment
                    freely.
                  </CardDescription>
                </CardContent>
              </Card>
              <Card className="border-none shadow-lg">
                <CardHeader>
                  <div className="rounded-full bg-blue-100 p-3 text-blue-700 w-fit mx-auto">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-6 w-6"
                    >
                      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
                      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
                    </svg>
                  </div>
                  <CardTitle className="text-center">Full Documentation</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    Complete guides, code examples, and tutorials to help you master MTN MoMo API integration.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* How It Works */}
        <section className="py-16 md:py-24 bg-gray-50">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">How Sandbox Works</h2>
              <p className="mt-4 text-gray-600 md:text-lg">
                Get started with MTN MoMo integration in just a few simple steps.
              </p>
            </div>
            <div className="grid gap-8 md:grid-cols-3">
              <div className="text-center">
                <div className="mx-auto rounded-full bg-blue-600 text-white w-12 h-12 flex items-center justify-center text-xl font-bold mb-4">
                  1
                </div>
                <h3 className="text-xl font-bold mb-2">Enter Sandbox</h3>
                <p className="text-gray-600">
                  Click "Enter Sandbox" to access the testing environment. No registration required - start immediately.
                </p>
              </div>
              <div className="text-center">
                <div className="mx-auto rounded-full bg-blue-600 text-white w-12 h-12 flex items-center justify-center text-xl font-bold mb-4">
                  2
                </div>
                <h3 className="text-xl font-bold mb-2">Get Test Funds</h3>
                <p className="text-gray-600">
                  Add virtual money to your sandbox wallet. Test different payment scenarios with unlimited virtual
                  funds.
                </p>
              </div>
              <div className="text-center">
                <div className="mx-auto rounded-full bg-blue-600 text-white w-12 h-12 flex items-center justify-center text-xl font-bold mb-4">
                  3
                </div>
                <h3 className="text-xl font-bold mb-2">Test & Learn</h3>
                <p className="text-gray-600">
                  Practice API calls, test payment flows, and build your integration with confidence before going live.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 md:py-24 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Ready to Start Learning?</h2>
              <p className="mt-6 text-lg text-blue-100 md:text-xl">
                Jump into our sandbox environment and start experimenting with MTN MoMo APIs today. No setup required,
                no risks involved.
              </p>
              <div className="mt-8 flex flex-col gap-4 sm:flex-row sm:justify-center">
                <Button size="lg" asChild className="bg-white text-blue-700 hover:bg-blue-50 rounded-full px-8">
                  <Link href="/login/sandbox">Enter Sandbox Now</Link>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  asChild
                  className="rounded-full px-8 border-white text-white hover:bg-white/10"
                >
                  <Link href="/docs">Read Documentation</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}
