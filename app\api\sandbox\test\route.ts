import { NextRequest, NextResponse } from 'next/server'
import { withStudentAuth } from '@/lib/api-auth'

/**
 * GET /api/sandbox/test
 * Test endpoint to verify authentication and basic functionality
 */
export const GET = withStudentAuth(async (request, auth) => {
  try {
    console.log('Test endpoint called for user:', auth.user.id)
    
    // Test database connection
    const { prisma } = await import('@/lib/prisma')
    const userCount = await prisma.user.count()
    console.log('Database connection successful, user count:', userCount)
    
    return NextResponse.json({
      success: true,
      data: {
        userId: auth.user.id,
        userEmail: auth.user.email,
        timestamp: new Date().toISOString(),
        databaseConnected: true,
        userCount
      },
      message: 'Test endpoint working correctly'
    })

  } catch (error) {
    console.error('Test endpoint error:', error)
    return NextResponse.json(
      { 
        error: 'Test endpoint failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
})
