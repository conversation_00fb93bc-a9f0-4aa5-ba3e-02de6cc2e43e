"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

export default function AdminDashboardPage() {
  const [selectedApplication, setSelectedApplication] = useState<any>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false)
  const [rejectionReason, setRejectionReason] = useState("")
  const [selectedDocument, setSelectedDocument] = useState<any>(null)
  const [isDocumentDialogOpen, setIsDocumentDialogOpen] = useState(false)
  const [userFilter, setUserFilter] = useState("all")
  const [universityFilter, setUniversityFilter] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [bulkAction, setBulkAction] = useState("")

  // Mock data - would come from API in real implementation
  const systemStats = {
    totalUsers: 127,
    activeUsers: 89,
    pendingKYC: 5,
    suspendedUsers: 0,
    totalTransactions: 1245,
    successRate: 98.2,
    apiCalls: 45678,
    sandboxBalance: 2500000,
  }

  const pendingApplications = [
    {
      id: "app_001",
      studentName: "John Doe",
      email: "<EMAIL>",
      university: "University of Yaoundé",
      program: "Computer Science",
      studentId: "*********",
      graduationYear: "2025",
      submittedAt: "2025-06-12T10:30:00Z",
      status: "pending",
      documents: {
        studentIdCard: "student_id_001.pdf",
        registrationReceipt: "receipt_001.pdf",
        passportPhoto: "photo_001.jpg",
        nationalId: "national_id_001.pdf",
      },
      projectInfo: {
        purpose: "Academic Project",
        name: "Campus Marketplace",
        description:
          "Building an e-commerce platform for campus community with MTN MoMo integration for seamless payments between students and local vendors.",
        githubLink: "https://github.com/johndoe/campus-marketplace",
      },
    },
    {
      id: "app_002",
      studentName: "Marie Nguemo",
      email: "<EMAIL>",
      university: "University of Douala",
      program: "Software Engineering",
      studentId: "*********",
      graduationYear: "2024",
      submittedAt: "2025-06-11T15:20:00Z",
      status: "pending",
      documents: {
        studentIdCard: "student_id_002.pdf",
        registrationReceipt: "receipt_002.pdf",
        passportPhoto: "photo_002.jpg",
      },
      projectInfo: {
        purpose: "Startup MVP",
        name: "Food Delivery App",
        description: "On-demand food delivery service for university students with mobile money payment integration",
        githubLink: "",
      },
    },
  ]

  const recentUsers = [
    {
      id: "user_001",
      name: "Alice Mbeki",
      email: "<EMAIL>",
      university: "University of Yaoundé",
      status: "verified",
      apiUsage: 245,
      lastActive: "2 hours ago",
    },
    {
      id: "user_002",
      name: "Paul Nkomo",
      email: "<EMAIL>",
      university: "University of Douala",
      status: "verified",
      apiUsage: 156,
      lastActive: "1 day ago",
    },
    {
      id: "user_003",
      name: "Grace Tabi",
      email: "<EMAIL>",
      university: "University of Buea",
      status: "pending",
      apiUsage: 0,
      lastActive: "3 days ago",
    },
  ]

  const allUsers = [
    ...recentUsers,
    {
      id: "user_004",
      name: "Samuel Kone",
      email: "<EMAIL>",
      university: "University of Yaoundé",
      status: "suspended",
      apiUsage: 0,
      lastActive: "1 week ago",
      suspendedReason: "Suspicious activity detected",
    },
    {
      id: "user_005",
      name: "Fatima Bello",
      email: "<EMAIL>",
      university: "University of Buea",
      status: "verified",
      apiUsage: 89,
      lastActive: "5 hours ago",
    },
  ]

  // Filter users based on current filters
  const filteredUsers = allUsers.filter((user) => {
    if (userFilter !== "all" && user.status !== userFilter) return false
    if (universityFilter !== "all" && !user.university.toLowerCase().includes(universityFilter)) return false
    if (
      searchQuery &&
      !user.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !user.email.toLowerCase().includes(searchQuery.toLowerCase())
    )
      return false
    return true
  })

  // System analytics data
  const systemAnalytics = {
    userGrowth: [
      { month: "Jan", users: 45 },
      { month: "Feb", users: 67 },
      { month: "Mar", users: 89 },
      { month: "Apr", users: 112 },
      { month: "May", users: 127 },
    ],
    apiUsageByUniversity: [
      { university: "University of Yaoundé", usage: 45 },
      { university: "University of Douala", usage: 32 },
      { university: "University of Buea", usage: 23 },
    ],
  }

  const handleSuspendUser = (userId: string, reason: string) => {
    console.log(`Suspending user ${userId} for: ${reason}`)
    // In real implementation: API call to suspend user
  }

  const handleUnsuspendUser = (userId: string) => {
    console.log(`Unsuspending user ${userId}`)
    // In real implementation: API call to unsuspend user
  }

  const handleResetApiKey = (userId: string) => {
    if (confirm("Are you sure you want to reset this user's API key? This will break their existing integrations.")) {
      console.log(`Resetting API key for user ${userId}`)
      // In real implementation: API call to reset API key
    }
  }

  const handleBulkAction = () => {
    if (selectedUsers.length === 0) {
      alert("Please select users first")
      return
    }

    if (bulkAction === "approve") {
      console.log(`Bulk approving users: ${selectedUsers}`)
    } else if (bulkAction === "reject") {
      console.log(`Bulk rejecting users: ${selectedUsers}`)
    }

    setSelectedUsers([])
    setBulkAction("")
  }

  const handleViewDocument = (document: any) => {
    setSelectedDocument(document)
    setIsDocumentDialogOpen(true)
  }

  const handleViewApplication = (application: any) => {
    setSelectedApplication(application)
    setIsDialogOpen(true)
  }

  const handleApproveApplication = (applicationId: string) => {
    console.log("Approving application:", applicationId)
    // In real implementation:
    // 1. Update application status to "approved"
    // 2. Send email to student with dashboard access link
    // 3. Create student account with verified status
    // 4. Generate initial API key
  }

  const handleRejectApplication = (applicationId: string, reason: string) => {
    console.log("Rejecting application:", applicationId, "Reason:", reason)
    setIsRejectDialogOpen(false)
    setRejectionReason("")
    // In real implementation:
    // 1. Update application status to "rejected"
    // 2. Send email to student with rejection reason
    // 3. Allow resubmission with corrections
  }

  return (
    <div className="flex min-h-screen flex-col bg-gray-100">
      <header className="border-b bg-white">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="relative flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-primary-600 to-primary-700 text-white">
              <span className="absolute -right-1 -top-1 flex h-3 w-3 items-center justify-center rounded-full bg-accent text-[8px] font-bold text-black">
                CM
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <path d="M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-7h-2c0-1-.5-1.5-1-2z" />
                <path d="M2 9v1c0 1.1.9 2 2 2h1" />
                <path d="M16 4h2a1 1 0 0 1 1 1" />
              </svg>
            </div>
            <span className="text-lg font-bold">
              StarterPay <span className="text-sm text-gray-500">Admin</span>
            </span>
          </div>
          <Button variant="outline">Sign Out</Button>
        </div>
      </header>
      <main className="flex-1 p-6">
        <div className="container">
          <div className="mb-8">
            <h1 className="text-3xl font-bold">Admin Dashboard</h1>
            <p className="text-gray-500">Manage student verifications and monitor system activity</p>
          </div>

          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="kyc">KYC Review ({pendingApplications.length})</TabsTrigger>
              <TabsTrigger value="users">User Management</TabsTrigger>
              <TabsTrigger value="analytics">System Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              {/* System Stats */}
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{systemStats.totalUsers}</div>
                    <p className="text-xs text-gray-500">Active: {systemStats.activeUsers}</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Pending KYC</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-warning">{systemStats.pendingKYC}</div>
                    <p className="text-xs text-gray-500">Awaiting review</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">API Success Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-success">{systemStats.successRate}%</div>
                    <p className="text-xs text-gray-500">Last 30 days</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Total API Calls</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{systemStats.apiCalls.toLocaleString()}</div>
                    <p className="text-xs text-gray-500">This month</p>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Activity */}
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent KYC Applications</CardTitle>
                    <CardDescription>Latest student verification requests</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {pendingApplications.slice(0, 3).map((app) => (
                        <div key={app.id} className="flex items-center justify-between border-b pb-2">
                          <div>
                            <p className="text-sm font-medium">{app.studentName}</p>
                            <p className="text-xs text-gray-500">{app.university}</p>
                          </div>
                          <div className="text-right">
                            <Badge variant="outline" className="bg-warning text-white">
                              Pending
                            </Badge>
                            <p className="text-xs text-gray-500 mt-1">
                              {new Date(app.submittedAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full">
                      View All Applications
                    </Button>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>System Health</CardTitle>
                    <CardDescription>Platform status and metrics</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">API Uptime</span>
                        <span className="text-sm font-medium text-success">99.9%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Avg Response Time</span>
                        <span className="text-sm font-medium">145ms</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Sandbox Balance</span>
                        <span className="text-sm font-medium">{systemStats.sandboxBalance.toLocaleString()} XAF</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Active Sessions</span>
                        <span className="text-sm font-medium">{systemStats.activeUsers}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="kyc">
              <Card>
                <CardHeader>
                  <CardTitle>KYC Verification Queue</CardTitle>
                  <CardDescription>Review and process student verification applications</CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Bulk Operations */}
                  <div className="flex items-center gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={selectedUsers.length === pendingApplications.length}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedUsers(pendingApplications.map((app) => app.id))
                          } else {
                            setSelectedUsers([])
                          }
                        }}
                      />
                      <span className="text-sm">Select All</span>
                    </div>

                    {selectedUsers.length > 0 && (
                      <div className="flex items-center gap-2">
                        <Select value={bulkAction} onValueChange={setBulkAction}>
                          <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Bulk action" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="approve">Approve Selected</SelectItem>
                            <SelectItem value="reject">Reject Selected</SelectItem>
                            <SelectItem value="request-info">Request More Info</SelectItem>
                          </SelectContent>
                        </Select>
                        <Button onClick={handleBulkAction} disabled={!bulkAction}>
                          Apply ({selectedUsers.length})
                        </Button>
                      </div>
                    )}
                  </div>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Student Name</TableHead>
                          <TableHead>University</TableHead>
                          <TableHead>Program</TableHead>
                          <TableHead>Student ID</TableHead>
                          <TableHead>Submitted</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {pendingApplications.map((application) => (
                          <TableRow key={application.id}>
                            <TableCell className="font-medium">{application.studentName}</TableCell>
                            <TableCell>{application.university}</TableCell>
                            <TableCell>{application.program}</TableCell>
                            <TableCell className="font-mono text-sm">{application.studentId}</TableCell>
                            <TableCell>{new Date(application.submittedAt).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <Badge variant="outline" className="bg-warning text-white">
                                Pending Review
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button variant="outline" size="sm" onClick={() => handleViewApplication(application)}>
                                  Review
                                </Button>
                                <Button
                                  size="sm"
                                  className="bg-success hover:bg-success/90"
                                  onClick={() => handleApproveApplication(application.id)}
                                >
                                  Approve
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedApplication(application)
                                    setIsRejectDialogOpen(true)
                                  }}
                                >
                                  Reject
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="users">
              <Card>
                <CardHeader>
                  <CardTitle>User Management</CardTitle>
                  <CardDescription>Manage all registered users and their access</CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Filters */}
                  <div className="flex gap-4 mb-6">
                    <Input
                      placeholder="Search users..."
                      className="max-w-sm"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <Select value={universityFilter} onValueChange={setUniversityFilter}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="All Universities" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Universities</SelectItem>
                        <SelectItem value="yaoundé">University of Yaoundé</SelectItem>
                        <SelectItem value="douala">University of Douala</SelectItem>
                        <SelectItem value="buea">University of Buea</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={userFilter} onValueChange={setUserFilter}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="verified">Verified</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="rejected">Rejected</SelectItem>
                        <SelectItem value="suspended">Suspended</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>University</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>API Usage</TableHead>
                          <TableHead>Last Active</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredUsers.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell className="font-medium">{user.name}</TableCell>
                            <TableCell>{user.email}</TableCell>
                            <TableCell>{user.university}</TableCell>
                            <TableCell>
                              <Badge
                                variant="outline"
                                className={
                                  user.status === "verified" ? "bg-success text-white" : "bg-warning text-white"
                                }
                              >
                                {user.status}
                              </Badge>
                            </TableCell>
                            <TableCell>{user.apiUsage} calls</TableCell>
                            <TableCell>{user.lastActive}</TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button variant="outline" size="sm">
                                  View Profile
                                </Button>
                                {user.status === "verified" && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleSuspendUser(user.id, "Manual suspension")}
                                  >
                                    Suspend
                                  </Button>
                                )}
                                {user.status === "suspended" && (
                                  <Button variant="outline" size="sm" onClick={() => handleUnsuspendUser(user.id)}>
                                    Unsuspend
                                  </Button>
                                )}
                                <Button variant="outline" size="sm" onClick={() => handleResetApiKey(user.id)}>
                                  Reset Key
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics">
              <div className="grid gap-6">
                {/* Enhanced System Metrics */}
                <Card>
                  <CardHeader>
                    <CardTitle>Platform Analytics</CardTitle>
                    <CardDescription>Comprehensive system metrics and trends</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="rounded-lg border bg-card p-4">
                        <div className="text-sm font-medium text-gray-500">User Growth Rate</div>
                        <div className="text-2xl font-bold text-success">+15%</div>
                        <div className="text-xs text-gray-400">This month</div>
                      </div>
                      <div className="rounded-lg border bg-card p-4">
                        <div className="text-sm font-medium text-gray-500">Avg. API Calls/User</div>
                        <div className="text-2xl font-bold">156</div>
                        <div className="text-xs text-gray-400">Per month</div>
                      </div>
                      <div className="rounded-lg border bg-card p-4">
                        <div className="text-sm font-medium text-gray-500">KYC Approval Rate</div>
                        <div className="text-2xl font-bold text-success">92%</div>
                        <div className="text-xs text-gray-400">Last 30 days</div>
                      </div>
                      <div className="rounded-lg border bg-card p-4">
                        <div className="text-sm font-medium text-gray-500">System Uptime</div>
                        <div className="text-2xl font-bold text-success">99.9%</div>
                        <div className="text-xs text-gray-400">This month</div>
                      </div>
                    </div>

                    {/* University Distribution */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-sm font-medium mb-3">API Usage by University</h4>
                        <div className="space-y-2">
                          {systemAnalytics.apiUsageByUniversity.map((item, index) => (
                            <div key={index} className="flex items-center justify-between">
                              <span className="text-sm">{item.university}</span>
                              <div className="flex items-center gap-2">
                                <div className="w-20 bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-primary h-2 rounded-full"
                                    style={{ width: `${(item.usage / 50) * 100}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium">{item.usage}%</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium mb-3">User Growth Trend</h4>
                        <div className="space-y-2">
                          {systemAnalytics.userGrowth.slice(-3).map((item, index) => (
                            <div key={index} className="flex items-center justify-between">
                              <span className="text-sm">{item.month}</span>
                              <span className="text-sm font-medium">{item.users} users</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Transaction Monitoring */}
                <Card>
                  <CardHeader>
                    <CardTitle>Transaction Monitoring</CardTitle>
                    <CardDescription>System-wide transaction oversight</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{systemStats.totalTransactions}</div>
                        <div className="text-sm text-gray-500">Total Transactions</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-success">{systemStats.successRate}%</div>
                        <div className="text-sm text-gray-500">Success Rate</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">2.5M XAF</div>
                        <div className="text-sm text-gray-500">Total Volume</div>
                      </div>
                    </div>

                    <div className="text-sm text-gray-600">
                      <p>• Average transaction value: 12,500 XAF</p>
                      <p>• Peak usage hours: 2PM - 6PM</p>
                      <p>• Most active university: University of Yaoundé</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      {/* Application Review Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        {selectedApplication && (
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Review Application - {selectedApplication.studentName}</DialogTitle>
              <DialogDescription>Application ID: {selectedApplication.id}</DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <h3 className="text-lg font-medium mb-4">Student Information</h3>
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Name</div>
                    <div className="text-sm">{selectedApplication.studentName}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Email</div>
                    <div className="text-sm">{selectedApplication.email}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">University</div>
                    <div className="text-sm">{selectedApplication.university}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Program</div>
                    <div className="text-sm">{selectedApplication.program}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Student ID</div>
                    <div className="text-sm font-mono">{selectedApplication.studentId}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Graduation Year</div>
                    <div className="text-sm">{selectedApplication.graduationYear}</div>
                  </div>
                </div>

                <h3 className="text-lg font-medium mb-4 mt-6">Project Information</h3>
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Purpose</div>
                    <div className="text-sm">{selectedApplication.projectInfo.purpose}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">Project Name</div>
                    <div className="text-sm">{selectedApplication.projectInfo.name || "Not provided"}</div>
                  </div>
                  <div className="col-span-2">
                    <div className="text-sm font-medium mb-1">Description</div>
                    <div className="text-sm bg-gray-50 p-2 rounded">{selectedApplication.projectInfo.description}</div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-sm font-medium">GitHub Link</div>
                    <div className="text-sm">
                      {selectedApplication.projectInfo.githubLink ? (
                        <a
                          href={selectedApplication.projectInfo.githubLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary-600 hover:underline"
                        >
                          View Repository
                        </a>
                      ) : (
                        "Not provided"
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Submitted Documents</h3>
                <div className="space-y-3">
                  <div className="border rounded-lg p-3">
                    <div className="text-sm font-medium">Student ID Card</div>
                    <div className="text-sm text-gray-500">{selectedApplication.documents.studentIdCard}</div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() =>
                        handleViewDocument({
                          type: "Student ID Card",
                          filename: selectedApplication.documents.studentIdCard,
                          studentName: selectedApplication.studentName,
                        })
                      }
                    >
                      View Document
                    </Button>
                  </div>
                  <div className="border rounded-lg p-3">
                    <div className="text-sm font-medium">Registration Receipt</div>
                    <div className="text-sm text-gray-500">{selectedApplication.documents.registrationReceipt}</div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() =>
                        handleViewDocument({
                          type: "Registration Receipt",
                          filename: selectedApplication.documents.registrationReceipt,
                          studentName: selectedApplication.studentName,
                        })
                      }
                    >
                      View Document
                    </Button>
                  </div>
                  <div className="border rounded-lg p-3">
                    <div className="text-sm font-medium">Passport Photo</div>
                    <div className="text-sm text-gray-500">{selectedApplication.documents.passportPhoto}</div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() =>
                        handleViewDocument({
                          type: "Passport Photo",
                          filename: selectedApplication.documents.passportPhoto,
                          studentName: selectedApplication.studentName,
                        })
                      }
                    >
                      View Document
                    </Button>
                  </div>
                  {selectedApplication.documents.nationalId && (
                    <div className="border rounded-lg p-3">
                      <div className="text-sm font-medium">National ID</div>
                      <div className="text-sm text-gray-500">{selectedApplication.documents.nationalId}</div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() =>
                          handleViewDocument({
                            type: "National ID",
                            filename: selectedApplication.documents.nationalId,
                            studentName: selectedApplication.studentName,
                          })
                        }
                      >
                        View Document
                      </Button>
                    </div>
                  )}
                </div>

                <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h4 className="text-sm font-medium text-yellow-800 mb-2">Review Checklist</h4>
                  <div className="space-y-1 text-sm text-yellow-700">
                    <div>✓ Valid university email domain</div>
                    <div>✓ Clear student ID document</div>
                    <div>✓ Academic project description</div>
                    <div>✓ Reasonable graduation timeline</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Close
              </Button>
              <Button
                className="bg-success hover:bg-success/90"
                onClick={() => {
                  handleApproveApplication(selectedApplication.id)
                  setIsDialogOpen(false)
                }}
              >
                Approve Application
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  setIsDialogOpen(false)
                  setIsRejectDialogOpen(true)
                }}
              >
                Reject Application
              </Button>
            </div>
          </DialogContent>
        )}
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Application</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this application. The student will receive this feedback.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Reason for Rejection</label>
              <Select onValueChange={setRejectionReason}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select a reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="invalid-documents">Invalid or unclear documents</SelectItem>
                  <SelectItem value="non-academic">Non-academic email address</SelectItem>
                  <SelectItem value="incomplete-info">Incomplete application information</SelectItem>
                  <SelectItem value="suspicious-activity">Suspicious activity detected</SelectItem>
                  <SelectItem value="other">Other (specify below)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Additional Comments</label>
              <Textarea
                placeholder="Provide specific feedback to help the student resubmit successfully..."
                className="mt-1"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2 mt-6">
            <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleRejectApplication(selectedApplication?.id, rejectionReason)}
            >
              Reject Application
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Document Viewing Dialog */}
      <Dialog open={isDocumentDialogOpen} onOpenChange={setIsDocumentDialogOpen}>
        {selectedDocument && (
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Document Viewer - {selectedDocument.type}</DialogTitle>
              <DialogDescription>
                Student: {selectedDocument.studentName} | File: {selectedDocument.filename}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="border rounded-lg p-4 bg-gray-50 text-center">
                <div className="text-4xl mb-2">📄</div>
                <p className="text-sm text-gray-600">Document preview would appear here</p>
                <p className="text-xs text-gray-500 mt-2">In production: PDF viewer, image display, or download link</p>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" className="flex-1">
                  Download Original
                </Button>
                <Button variant="outline" className="flex-1">
                  Flag for Review
                </Button>
              </div>
            </div>
            <div className="flex justify-end">
              <Button onClick={() => setIsDocumentDialogOpen(false)}>Close</Button>
            </div>
          </DialogContent>
        )}
      </Dialog>
    </div>
  )
}
