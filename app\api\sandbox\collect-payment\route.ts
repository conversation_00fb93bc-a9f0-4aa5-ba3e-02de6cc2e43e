import { NextRequest, NextResponse } from 'next/server'
import { SandboxService, SandboxError } from '@/lib/sandbox-service'
import { z } from 'zod'

const collectPaymentSchema = z.object({
  amount: z.number().positive().max(1000000), // Max 1M XAF
  currency: z.string().optional().default('XAF'),
  externalId: z.string().min(1).max(50),
  payerPhone: z.string().regex(/^237[0-9]{8}$/, 'Phone must be in format 237XXXXXXXX'),
  payerMessage: z.string().optional(),
  payeeNote: z.string().optional(),
  callbackUrl: z.string().url().optional()
})

/**
 * POST /api/sandbox/collect-payment
 * Main endpoint for students to collect payments
 */
export async function POST(request: NextRequest) {
  try {
    // Get API key from Authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const apiKey = authHeader.substring(7) // Remove "Bearer "
    
    // Validate API key
    const sandboxService = new SandboxService()
    const keyValidation = await sandboxService.validateApiKey(apiKey)
    
    if (!keyValidation) {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = collectPaymentSchema.parse(body)

    // Process payment collection
    const result = await sandboxService.collectPayment(
      validatedData,
      keyValidation.userId,
      keyValidation.keyId
    )

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('Collect payment error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation error',
          details: error.errors
        },
        { status: 400 }
      )
    }

    if (error instanceof SandboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code
        },
        { status: error.statusCode }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/sandbox/collect-payment
 * Get API documentation
 */
export async function GET() {
  return NextResponse.json({
    name: 'StarterPay Sandbox Collections API',
    version: '1.0.0',
    description: 'Collect payments using MTN MoMo in sandbox environment',
    endpoints: {
      'POST /api/sandbox/collect-payment': {
        description: 'Initiate a payment collection',
        headers: {
          'Authorization': 'Bearer YOUR_SANDBOX_API_KEY',
          'Content-Type': 'application/json'
        },
        body: {
          amount: 'number (required) - Amount in XAF',
          currency: 'string (optional) - Currency code, defaults to XAF',
          externalId: 'string (required) - Your unique transaction ID',
          payerPhone: 'string (required) - Payer phone in format 237XXXXXXXX',
          payerMessage: 'string (optional) - Message to show payer',
          payeeNote: 'string (optional) - Note for your records',
          callbackUrl: 'string (optional) - URL to receive status updates'
        },
        response: {
          success: true,
          data: {
            transactionId: 'string - StarterPay transaction ID',
            status: 'PENDING | SUCCESSFUL | FAILED',
            amount: 'number',
            currency: 'string',
            externalId: 'string',
            payerPhone: 'string',
            createdAt: 'string (ISO date)'
          }
        }
      },
      'GET /api/sandbox/transactions/{id}': {
        description: 'Get transaction status',
        headers: {
          'Authorization': 'Bearer YOUR_SANDBOX_API_KEY'
        }
      }
    },
    examples: {
      request: {
        amount: 1000,
        currency: 'XAF',
        externalId: 'order_123',
        payerPhone: '************',
        payerMessage: 'Payment for order #123',
        payeeNote: 'E-commerce purchase'
      },
      response: {
        success: true,
        data: {
          transactionId: 'uuid-here',
          status: 'PENDING',
          amount: 1000,
          currency: 'XAF',
          externalId: 'order_123',
          payerPhone: '************',
          createdAt: '2024-01-01T12:00:00Z'
        }
      }
    }
  })
}
