export function HowItWorks() {
  const steps = [
    {
      number: 1,
      title: "Register & Verify",
      description: "Create an account and verify your student status with your institution credentials.",
    },
    {
      number: 2,
      title: "Get Your API Key",
      description: "Once verified, generate your API key to start integrating with MTN MoMo.",
    },
    {
      number: 3,
      title: "Build & Test",
      description: "Use our documentation to build and test your payment integration.",
    },
    {
      number: 4,
      title: "Launch Your Project",
      description: "Deploy your application with real MTN MoMo payment capabilities.",
    },
  ]

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-white to-primary-100">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-full bg-primary-100 px-3 py-1 text-sm font-medium text-primary-700">
              Simple Process
            </div>
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">How It Works</h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed">
              Get up and running with MTN MoMo APIs in just a few simple steps.
            </p>
          </div>
        </div>
        <div className="relative mt-16">
          <div className="relative grid grid-cols-1 gap-12 md:grid-cols-4">
            {steps.map((step, index) => (
              <div key={index} className="flex flex-col items-center space-y-4 relative">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-primary-600 to-primary-700 text-white text-xl font-bold shadow-lg z-10">
                  {step.number}
                </div>
                <h3 className="text-xl font-bold">{step.title}</h3>
                <p className="text-sm text-gray-500 text-center">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
