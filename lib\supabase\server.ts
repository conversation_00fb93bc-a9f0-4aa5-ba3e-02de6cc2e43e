import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  const cookieStore = await cookies()

  // Debug: log env vars once in non-production to ensure they are loaded correctly
  if (process.env.NODE_ENV !== 'production') {
    console.log(
      'SUPABASE URL →', process.env.NEXT_PUBLIC_SUPABASE_URL,
      '\nSUPABASE KEY →', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.slice(0, 8)
    )
  }

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}
