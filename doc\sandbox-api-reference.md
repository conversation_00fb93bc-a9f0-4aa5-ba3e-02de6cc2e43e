# StarterPay Sandbox API Reference

## 🚀 Quick Start

This reference covers the updated sandbox API endpoints that power the real-data dashboard.

## 🔐 Authentication

All sandbox endpoints require student authentication:

```typescript
// All requests must include valid session/auth
// Handled automatically by withStudentAuth middleware
```

## 📊 Wallet API

### Get Wallet Information

```http
GET /api/sandbox/wallet
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "wallet_uuid",
    "balance": 100000,
    "currency": "XAF",
    "createdAt": "2024-12-26T10:00:00Z",
    "recentTransactions": [
      {
        "id": "txn_uuid",
        "externalId": "ext_001",
        "amount": 10000,
        "currency": "XAF",
        "status": "SUCCESSFUL",
        "type": "COLLECTION",
        "createdAt": "2024-12-26T09:30:00Z",
        "completedAt": "2024-12-26T09:30:45Z"
      }
    ]
  }
}
```

**Error Response:**
```json
{
  "error": "Wallet not found",
  "code": "WALLET_NOT_FOUND"
}
```

## 🔑 API Key Management

### List API Keys

```http
GET /api/sandbox/api-keys
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "key_uuid",
      "keyName": "My Development Key",
      "environment": "sandbox",
      "isActive": true,
      "usageCount": 25,
      "rateLimit": 100,
      "createdAt": "2024-12-26T08:00:00Z",
      "lastUsedAt": "2024-12-26T09:45:00Z"
    }
  ]
}
```

### Create API Key

```http
POST /api/sandbox/api-keys
Content-Type: application/json

{
  "keyName": "My New API Key"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "key_uuid",
    "keyName": "My New API Key",
    "apiKey": "sk_sandbox_1234567890abcdef...",
    "environment": "sandbox",
    "rateLimit": 100
  },
  "message": "API key created successfully. Save this key securely - it will not be shown again."
}
```

**Validation Errors:**
```json
{
  "error": "Validation error",
  "details": [
    {
      "path": ["keyName"],
      "message": "Key name is required"
    }
  ]
}
```

## 📋 Transactions API

### List Transactions

```http
GET /api/sandbox/transactions?limit=50&offset=0
```

**Query Parameters:**
- `limit` (optional): Number of transactions to return (max 100, default 50)
- `offset` (optional): Number of transactions to skip (default 0)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "txn_uuid",
      "externalId": "ext_001",
      "amount": 10000,
      "currency": "XAF",
      "type": "COLLECTION",
      "status": "SUCCESSFUL",
      "mtnTransactionId": "mtn_txn_123",
      "mtnReferenceId": "ref_uuid",
      "payerPhone": "+************",
      "payerMessage": "Payment for services",
      "payeeNote": "Service payment",
      "failureReason": null,
      "createdAt": "2024-12-26T09:30:00Z",
      "updatedAt": "2024-12-26T09:30:45Z",
      "completedAt": "2024-12-26T09:30:45Z"
    }
  ],
  "pagination": {
    "limit": 50,
    "offset": 0,
    "total": 1
  }
}
```

## 🔄 Transaction Status Values

| Status | Description |
|--------|-------------|
| `PENDING` | Transaction initiated, waiting for completion |
| `SUCCESSFUL` | Transaction completed successfully |
| `FAILED` | Transaction failed |
| `CANCELLED` | Transaction was cancelled |

## 🏗️ Data Models

### Wallet Model
```typescript
interface WalletData {
  id: string
  balance: number
  currency: string
  createdAt: string
  recentTransactions: Transaction[]
}
```

### API Key Model
```typescript
interface ApiKeyData {
  id: string
  keyName: string
  environment: string
  isActive: boolean
  usageCount: number
  rateLimit: number
  createdAt: string
  lastUsedAt: string | null
}
```

### Transaction Model
```typescript
interface Transaction {
  id: string
  externalId: string
  amount: number
  currency: string
  status: 'PENDING' | 'SUCCESSFUL' | 'FAILED' | 'CANCELLED'
  type: 'COLLECTION'
  createdAt: string
  completedAt: string | null
  payerPhone?: string
  payerMessage?: string
  payeeNote?: string
  failureReason?: string
  mtnTransactionId?: string
  mtnReferenceId: string
}
```

## 🚨 Error Handling

### Standard Error Response
```json
{
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

### Common Error Codes
- `UNAUTHENTICATED` - User not authenticated
- `WALLET_NOT_FOUND` - User wallet not found
- `VALIDATION_ERROR` - Request validation failed
- `INTERNAL_ERROR` - Server error

## 🔧 Frontend Integration

### React Hook Example
```typescript
const useSandboxWallet = () => {
  const [wallet, setWallet] = useState<WalletData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchWallet = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/sandbox/wallet')
      if (!response.ok) throw new Error('Failed to fetch wallet')
      
      const result = await response.json()
      if (result.success) {
        setWallet(result.data)
      } else {
        throw new Error(result.error)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchWallet()
  }, [])

  return { wallet, loading, error, refetch: fetchWallet }
}
```

## 🔒 Security Notes

1. **API Keys**: Stored as hashed values in database
2. **User Isolation**: Users can only access their own data
3. **Rate Limiting**: API keys have usage limits
4. **Authentication**: All endpoints require valid session

## 📚 Related Guides

- [Sandbox Dashboard Guide](./sandbox-dashboard-guide.md)
- [MTN MoMo Integration](./sandbox-implementation-plan.md)
- [Database Schema](./starterpay_data_model.md)

---

**Last Updated**: December 2024  
**Version**: 2.0 (Real Data Integration)
