"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"

const profileFormSchema = z.object({
  fullName: z.string().min(2, {
    message: "Full name must be at least 2 characters.",
  }),
  email: z
    .string()
    .email({
      message: "Please enter a valid email address.",
    })
    .optional(),
  studentId: z
    .string()
    .min(2, {
      message: "Student ID must be at least 2 characters.",
    })
    .optional(),
  institution: z
    .string()
    .min(1, {
      message: "Please enter your institution.",
    })
    .optional(),
  phoneNumber: z.string().min(10, {
    message: "Phone number must be at least 10 characters.",
  }),
})

const passwordFormSchema = z
  .object({
    currentPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
    newPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
    confirmPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  })

export default function ProfilePage() {
  const [isUpdating, setIsUpdating] = useState(false)
  const [isChangingPassword, setIsChangingPassword] = useState(false)

  // Mock data - would come from API in real implementation
  const userData = {
    fullName: "John Doe",
    email: "<EMAIL>",
    studentId: "S12345",
    institution: "University of Yaoundé",
    phoneNumber: "237612345678",
    verificationStatus: "verified",
    createdAt: "2025-06-01",
  }

  const profileForm = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      fullName: userData.fullName,
      email: userData.email,
      studentId: userData.studentId,
      institution: userData.institution,
      phoneNumber: userData.phoneNumber,
    },
  })

  const passwordForm = useForm<z.infer<typeof passwordFormSchema>>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  })

  function onProfileSubmit(values: z.infer<typeof profileFormSchema>) {
    setIsUpdating(true);

    // TODO: replace with actual API call
    setTimeout(() => {
      setIsUpdating(false);
    }, 1000);
  }

  function onPasswordSubmit(values: z.infer<typeof passwordFormSchema>) {
    setIsChangingPassword(true);

    // TODO: replace with actual API call
    setTimeout(() => {
      setIsChangingPassword(false);
    }, 1000);
  }

  return (
    <div className="container py-8">
      <h1 className="mb-6 text-2xl font-bold">Profile</h1>

      {/* Profile form */}
      <form
        className="space-y-4"
        onSubmit={profileForm.handleSubmit(onProfileSubmit)}
      >
        <input
          type="text"
          placeholder="Full name"
          {...profileForm.register("fullName")}
          className="border p-2 w-full"
        />
        <input
          type="email"
          placeholder="Email address"
          {...profileForm.register("email")}
          className="border p-2 w-full"
        />
        <input
          type="text"
          placeholder="Student ID"
          {...profileForm.register("studentId")}
          className="border p-2 w-full"
        />
        <input
          type="text"
          placeholder="Institution"
          {...profileForm.register("institution")}
          className="border p-2 w-full"
        />
        <input
          type="tel"
          placeholder="Phone number"
          {...profileForm.register("phoneNumber")}
          className="border p-2 w-full"
        />
        <button
          type="submit"
          disabled={isUpdating}
          className="rounded bg-primary-500 px-4 py-2 text-white disabled:opacity-50"
        >
          {isUpdating ? "Updating..." : "Update Profile"}
        </button>
      </form>

      {/* Change password */}
      <h2 className="mt-10 mb-4 text-xl font-semibold">Change Password</h2>
      <form
        className="space-y-4"
        onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
      >
        <input
          type="password"
          placeholder="Current password"
          {...passwordForm.register("currentPassword")}
          className="border p-2 w-full"
        />
        <input
          type="password"
          placeholder="New password"
          {...passwordForm.register("newPassword")}
          className="border p-2 w-full"
        />
        <input
          type="password"
          placeholder="Confirm new password"
          {...passwordForm.register("confirmPassword")}
          className="border p-2 w-full"
        />
        <button
          type="submit"
          disabled={isChangingPassword}
          className="rounded bg-primary-500 px-4 py-2 text-white disabled:opacity-50"
        >
          {isChangingPassword ? "Saving..." : "Save"}
        </button>
      </form>
    </div>
  );
}
