"use server"

import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"
import { prisma } from "@/lib/prisma"

export async function signup(formData: FormData) {
  // Extract fields from formData
  const data = {
    email: formData.get("email") as string,
    password: formData.get("password") as string,
    full_name: formData.get("fullName") as string,
    phone_number: formData.get("phoneNumber") as string,
    location: formData.get("location") as string,
    profession: formData.get("profession") as string,
    organization: (formData.get("organization") as string) || null,
    experience: formData.get("experience") as string,
    motivation: formData.get("motivation") as string,
    availability: formData.get("availability") as string,
  }

  const supabase = await createClient()

  const { data: authData, error } = await supabase.auth.signUp({
    email: data.email,
    password: data.password,
    options: {
      data: {
        full_name: data.full_name,
        phone_number: data.phone_number,
      },
      emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"}/admin/login`,
    },
  })

  if (error) {
    console.error("Admin signup error:", error)
    redirect("/error")
  }

  if (authData.user) {
    try {
      await prisma.user.create({
        data: {
          id: authData.user.id,
          role: "ADMIN",
          adminProfile: {
            create: {
              fullName: data.full_name,
              email: data.email,
              phoneNumber: data.phone_number,
              location: data.location,
              profession: data.profession,
              organization: data.organization,
              experience: data.experience,
              motivation: data.motivation,
              availability: data.availability,
            },
          },
        },
      })
      console.log("Admin profile created for", authData.user.id)
    } catch (e) {
      console.error("Failed to create admin profile:", e)
    }
  }

  redirect("/admin/register/success")
}
