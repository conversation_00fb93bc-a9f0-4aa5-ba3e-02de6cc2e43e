import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"
import Image from "next/image"

export default function AboutPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-b from-white to-primary-50 py-16 sm:py-24">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center">
              <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl">
                Empowering the Next Generation of
                <span className="text-primary-600"> Cameroonian Innovators</span>
              </h1>
              <p className="mt-6 text-lg text-gray-600 md:text-xl">
                Born from a student's frustration, built for a community's dreams. StarterPay is democratizing access to
                mobile payment APIs for student developers across Cameroon.
              </p>
            </div>
          </div>
        </section>

        {/* Origin Story Section */}
        <section className="py-16 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="grid gap-12 lg:grid-cols-2 lg:gap-16 items-center">
              <div className="space-y-6">
                <div className="inline-block rounded-full bg-primary-100 px-3 py-1 text-sm font-medium text-primary-700">
                  Our Story
                </div>
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">It Started with a Simple Dream</h2>
                <div className="space-y-4 text-gray-600">
                  <p>
                    Picture this: A final year software engineering student, burning the midnight oil, putting the
                    finishing touches on his MVP. The application was ready, the code was clean, and the vision was
                    clear. There was just one problem – he needed to test mobile money payments to bring his idea to
                    life.
                  </p>
                  <p>
                    What should have been a simple integration turned into a bureaucratic nightmare. Business
                    registration requirements, complex documentation, lengthy approval processes – all barriers that
                    seemed designed for established companies, not ambitious students with innovative ideas.
                  </p>
                  <p>
                    That student realized he wasn't alone. Across Cameroon's universities, countless brilliant minds
                    were hitting the same wall. Amazing projects were gathering dust, not because they lacked merit, but
                    because they lacked access.
                  </p>
                  <p className="font-medium text-primary-700">
                    That's when StarterPay was born – not just as a solution, but as a movement.
                  </p>
                </div>
              </div>
              <div className="relative">
                <div className="absolute -inset-1 rounded-xl bg-gradient-to-r from-primary-600 to-secondary blur-xl opacity-30"></div>
                <div className="relative rounded-xl overflow-hidden shadow-2xl">
                  <Image
                    src="/placeholder.svg?height=400&width=500&text=Student+Developer+Story"
                    alt="Student Developer Working Late"
                    width={500}
                    height={400}
                    className="object-cover"
                  />
                </div>
                <div className="absolute -bottom-4 -right-4 rounded-lg bg-white p-4 shadow-lg">
                  <div className="flex items-center space-x-2">
                    <div className="h-3 w-3 rounded-full bg-primary-600"></div>
                    <span className="text-sm font-medium">Innovation Born</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mission & Vision Section */}
        <section className="py-16 md:py-24 bg-gradient-to-b from-primary-50 to-white">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Our Mission & Vision</h2>
              <p className="mt-4 text-gray-600 md:text-lg">
                Driven by purpose, guided by the belief that innovation shouldn't be limited by bureaucracy.
              </p>
            </div>
            <div className="grid gap-8 md:grid-cols-2">
              <Card className="border-none shadow-lg bg-gradient-to-br from-primary-600 to-primary-700 text-white">
                <CardContent className="p-8">
                  <div className="mb-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-12 w-12 text-accent"
                    >
                      <path d="M12 2L2 7l10 5 10-5-10-5z" />
                      <path d="M2 17l10 5 10-5" />
                      <path d="M2 12l10 5 10-5" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Our Mission</h3>
                  <p className="text-primary-100">
                    To democratize access to mobile payment APIs for Cameroonian student developers, removing barriers
                    and enabling innovation. We believe every student with a great idea deserves the tools to bring it
                    to life, regardless of their institutional or financial status.
                  </p>
                </CardContent>
              </Card>
              <Card className="border-none shadow-lg">
                <CardContent className="p-8">
                  <div className="mb-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-12 w-12 text-primary-600"
                    >
                      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
                      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-gray-900">Our Vision</h3>
                  <p className="text-gray-600">
                    To see Cameroon become a leading hub of fintech innovation in Africa, powered by a generation of
                    student entrepreneurs who had the freedom to experiment, learn, and build without traditional
                    barriers limiting their potential.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* The Problem We Solve */}
        <section className="py-16 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center mb-12">
              <div className="inline-block rounded-full bg-secondary/10 px-3 py-1 text-sm font-medium text-secondary mb-4">
                The Challenge
              </div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Breaking Down the Barriers</h2>
              <p className="mt-4 text-gray-600 md:text-lg">
                Traditional API access requirements weren't designed with student innovators in mind.
              </p>
            </div>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center space-y-4">
                <div className="mx-auto rounded-full bg-secondary/10 p-4 w-16 h-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-secondary"
                  >
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                    <polyline points="14 2 14 8 20 8" />
                    <line x1="16" y1="13" x2="8" y2="13" />
                    <line x1="16" y1="17" x2="8" y2="17" />
                    <polyline points="10 9 9 9 8 9" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Complex Documentation</h3>
                <p className="text-gray-600 text-sm">
                  Business registration papers, tax certificates, and corporate documentation that students simply don't
                  have access to.
                </p>
              </div>
              <div className="text-center space-y-4">
                <div className="mx-auto rounded-full bg-secondary/10 p-4 w-16 h-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-secondary"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <polyline points="12 6 12 12 16 14" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Lengthy Approval Process</h3>
                <p className="text-gray-600 text-sm">
                  Weeks or months of waiting for approval, killing the momentum and excitement that drives student
                  innovation.
                </p>
              </div>
              <div className="text-center space-y-4">
                <div className="mx-auto rounded-full bg-secondary/10 p-4 w-16 h-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-secondary"
                  >
                    <line x1="12" y1="1" x2="12" y2="23" />
                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Financial Barriers</h3>
                <p className="text-gray-600 text-sm">
                  High setup costs and minimum transaction requirements that are unrealistic for student projects and
                  prototypes.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Our Impact */}
        <section className="py-16 md:py-24 bg-gradient-to-b from-white to-primary-50">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center mb-12">
              <div className="inline-block rounded-full bg-success/10 px-3 py-1 text-sm font-medium text-success mb-4">
                Our Impact
              </div>
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Transforming Dreams into Reality</h2>
              <p className="mt-4 text-gray-600 md:text-lg">
                Every number represents a student who didn't give up on their idea.
              </p>
            </div>
            <div className="grid gap-8 md:grid-cols-4">
              <div className="text-center">
                <div className="text-4xl font-bold text-primary-600 mb-2">500+</div>
                <div className="text-sm text-gray-600">Student Developers</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-primary-600 mb-2">50+</div>
                <div className="text-sm text-gray-600">Universities</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-primary-600 mb-2">200+</div>
                <div className="text-sm text-gray-600">Projects Launched</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-primary-600 mb-2">15+</div>
                <div className="text-sm text-gray-600">Startups Born</div>
              </div>
            </div>
          </div>
        </section>

        {/* Our Values */}
        <section className="py-16 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">What We Stand For</h2>
              <p className="mt-4 text-gray-600 md:text-lg">
                Our values guide every decision we make and every feature we build.
              </p>
            </div>
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              <div className="text-center space-y-4">
                <div className="mx-auto rounded-full bg-primary-100 p-4 w-16 h-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-primary-600"
                  >
                    <path d="M12 2L2 7l10 5 10-5-10-5z" />
                    <path d="M2 17l10 5 10-5" />
                    <path d="M2 12l10 5 10-5" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Accessibility</h3>
                <p className="text-gray-600 text-sm">
                  Innovation shouldn't be a privilege. We make powerful tools accessible to every student, regardless of
                  background.
                </p>
              </div>
              <div className="text-center space-y-4">
                <div className="mx-auto rounded-full bg-primary-100 p-4 w-16 h-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-primary-600"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Community</h3>
                <p className="text-gray-600 text-sm">
                  We're building more than a platform – we're fostering a community of innovators who support each
                  other.
                </p>
              </div>
              <div className="text-center space-y-4">
                <div className="mx-auto rounded-full bg-primary-100 p-4 w-16 h-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-primary-600"
                  >
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Simplicity</h3>
                <p className="text-gray-600 text-sm">
                  Complex problems deserve simple solutions. We eliminate unnecessary complexity at every step.
                </p>
              </div>
              <div className="text-center space-y-4">
                <div className="mx-auto rounded-full bg-primary-100 p-4 w-16 h-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-primary-600"
                  >
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10" />
                    <path d="M9 12l2 2 4-4" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Trust</h3>
                <p className="text-gray-600 text-sm">
                  We handle your projects and data with the highest level of security and transparency.
                </p>
              </div>
              <div className="text-center space-y-4">
                <div className="mx-auto rounded-full bg-primary-100 p-4 w-16 h-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-primary-600"
                  >
                    <path d="M8 3L4 7l4 4" />
                    <path d="M4 7h16" />
                    <path d="M16 21l4-4-4-4" />
                    <path d="M20 17H4" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Growth</h3>
                <p className="text-gray-600 text-sm">
                  We grow with our community, constantly evolving to meet the changing needs of student developers.
                </p>
              </div>
              <div className="text-center space-y-4">
                <div className="mx-auto rounded-full bg-primary-100 p-4 w-16 h-16 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-8 w-8 text-primary-600"
                  >
                    <path d="M9 12l2 2 4-4" />
                    <path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                    <path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                    <path d="M12 21c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                    <path d="M12 3c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold">Excellence</h3>
                <p className="text-gray-600 text-sm">
                  We strive for excellence in everything we do, from our API reliability to our customer support.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 md:py-24 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
          <div className="container px-4 md:px-6">
            <div className="mx-auto max-w-3xl text-center">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Ready to Join the Movement?
              </h2>
              <p className="mt-6 text-lg text-primary-100 md:text-xl">
                Whether you're a student with the next big idea or an institution looking to support innovation, we'd
                love to have you as part of our community.
              </p>
              <div className="mt-8 flex flex-col gap-4 sm:flex-row sm:justify-center">
                <Button size="lg" asChild className="bg-white text-primary-700 hover:bg-primary-50 rounded-full px-8">
                  <Link href="/register">Start Building Today</Link>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  asChild
                  className="rounded-full px-8 border-white text-white hover:bg-white/10"
                >
                  <Link href="/contact">Partner With Us</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  )
}
