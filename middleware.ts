import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import { UserType, KycStatus } from '@prisma/client'
import { prisma } from './lib/prisma'

// Define route access rules
const ROUTE_ACCESS = {
  // Public routes - accessible to everyone
  public: [
    '/',
    '/about',
    '/docs',
    '/sandbox-info',
    '/login',
    '/register',
    '/admin/login',
    '/admin/register'
  ],

  // Student-only routes
  student: [
    '/dashboard',
    '/sandbox/dashboard',
    '/profile',
    '/api-keys',
    '/transactions',
    '/onboarding',
    '/kyc'
  ],

  // Admin-only routes
  admin: [
    '/admin/dashboard',
    '/admin'
  ],

  // API routes that need special handling
  api: [
    '/api'
  ]
} as const

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) =>
            request.cookies.set(name, value)
          )
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const {
    data: { user },
  } = await supabase.auth.getUser()

  const { pathname } = request.nextUrl

  // Helper function to check if route matches pattern
  const matchesRoute = (path: string, patterns: readonly string[]): boolean => {
    return patterns.some(pattern => path.startsWith(pattern))
  }

  // Check if it's a public route
  if (matchesRoute(pathname, ROUTE_ACCESS.public)) {
    return supabaseResponse
  }

  // Handle authenticated users
  if (user) {
    try {
      const dbUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { role: true },
      })

      if (!dbUser) {
        // User exists in Supabase but not in our database
        console.warn(`User ${user.id} found in Supabase but not in database`)
        return NextResponse.redirect(new URL('/login', request.url))
      }

      const userRole = dbUser.role

      // Role-based route access control
      if (userRole === UserType.ADMIN) {
        // Admin trying to access student routes
        if (matchesRoute(pathname, ROUTE_ACCESS.student)) {
          console.log(`Admin user ${user.id} trying to access student route ${pathname}, redirecting to admin dashboard`)
          return NextResponse.redirect(new URL('/admin/dashboard', request.url))
        }
      } else if (userRole === UserType.STUDENT) {
        // Student trying to access admin routes
        if (matchesRoute(pathname, ROUTE_ACCESS.admin)) {
          console.log(`Student user ${user.id} trying to access admin route ${pathname}, redirecting to student dashboard`)
          return NextResponse.redirect(new URL('/dashboard', request.url))
        }

        // Check KYC status for production dashboard access
        if (pathname === '/dashboard') {
          const studentProfile = await prisma.studentProfile.findUnique({
            where: { userId: user.id },
            select: { kycStatus: true }
          })

          if (studentProfile) {
            switch (studentProfile.kycStatus) {
              case KycStatus.PENDING_REVIEW:
                return NextResponse.redirect(new URL('/kyc/awaiting-review', request.url))
              case KycStatus.REJECTED:
                return NextResponse.redirect(new URL('/kyc/rejected', request.url))
              case KycStatus.IN_PROGRESS:
              case KycStatus.NOT_STARTED:
                return NextResponse.redirect(new URL('/kyc/start', request.url))
              case KycStatus.APPROVED:
                // Allow access to dashboard
                break
            }
          }
        }
      }

      // Add user info to headers for API routes
      if (matchesRoute(pathname, ROUTE_ACCESS.api)) {
        const response = NextResponse.next({ request })
        response.headers.set('x-user-id', user.id)
        response.headers.set('x-user-role', userRole)
        return response
      }

    } catch (error) {
      console.error('Error in middleware user lookup:', error)
      // On database error, redirect to login for safety
      return NextResponse.redirect(new URL('/login', request.url))
    }
  } else {
    // Handle unauthenticated users
    const requiresAuth = matchesRoute(pathname, [...ROUTE_ACCESS.student, ...ROUTE_ACCESS.admin])

    if (requiresAuth) {
      // Store the attempted URL to redirect after login
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirectTo', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // Block API access for unauthenticated users (except auth endpoints)
    if (matchesRoute(pathname, ROUTE_ACCESS.api) && !pathname.startsWith('/api/auth')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object instead of the supabaseResponse object

  return supabaseResponse
}

export const runtime = 'nodejs'

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
