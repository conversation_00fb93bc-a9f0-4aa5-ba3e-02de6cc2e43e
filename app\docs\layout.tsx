import type React from "react"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON>er } from "@/components/footer"
import { DocsNav } from "@/components/docs-nav"

export default function DocsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <div className="flex-1 flex">
        <DocsNav />
        <main className="flex-1 overflow-auto">{children}</main>
      </div>
      <Footer />
    </div>
  )
}
