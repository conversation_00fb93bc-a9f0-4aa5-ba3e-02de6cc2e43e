# StarterPay Sandbox Dashboard Guide

## 🎯 Overview

The StarterPay Sandbox Dashboard has been completely updated to use real data instead of mock data. This guide covers the new functionality, API integration, and how to use the dashboard effectively.

## 🚀 What's New

### ✅ Real Data Integration
- **Live Wallet Balance**: Shows actual balance from database transactions
- **Real API Keys**: Create, view, and manage actual API keys
- **Live Transactions**: View real sandbox transactions with MTN MoMo integration
- **Dynamic Statistics**: All stats calculated from real data

### ✅ Removed Features
- **Add Funds Functionality**: Removed as sandbox balance should reflect real transactions only
- **Mock Data**: All placeholder data replaced with live API calls

### ✅ Enhanced User Experience
- **Loading States**: Proper spinners while data loads
- **Error Handling**: Clear error messages with retry options
- **Success Notifications**: Feedback for important actions
- **Empty States**: Helpful guidance when no data exists

## 📊 Dashboard Pages

### 1. Overview Dashboard (`/sandbox/dashboard`)

**Features:**
- Live wallet balance from database
- API key status and usage statistics
- Recent transaction summary
- Sandbox environment indicators

**Data Sources:**
- `GET /api/sandbox/wallet` - Wallet balance and recent transactions
- `GET /api/sandbox/api-keys` - API key information

**Loading States:**
- Spinner while fetching data
- Error state with retry button
- Graceful fallbacks for missing data

### 2. API Access (`/sandbox/dashboard/api-access`)

**Features:**
- Create new API keys with custom names
- View all API keys (without exposing actual key values)
- Copy API keys to clipboard
- View usage statistics and metadata
- Security guidelines and best practices

**API Integration:**
- `POST /api/sandbox/api-keys` - Create new API key
- `GET /api/sandbox/api-keys` - List user's API keys

**Key Management:**
- API keys are hashed in database for security
- Only shown once during creation
- Usage tracking and rate limiting information

### 3. Virtual Wallet (`/sandbox/dashboard/wallet`)

**Features:**
- Live balance display from database
- Transaction history from real data
- Refresh functionality to update data
- Sandbox currency indicators

**Data Sources:**
- `GET /api/sandbox/wallet` - Complete wallet information
- Includes recent transactions and balance

**Important Changes:**
- No more "Add Funds" button (removed)
- Balance reflects actual sandbox transactions
- "Reset Wallet" changed to "Refresh Data"

### 4. Transactions (`/sandbox/dashboard/transactions`)

**Features:**
- Real transaction listing from database
- Search and filter functionality
- Detailed transaction views
- MTN MoMo integration data
- Transaction status tracking

**API Integration:**
- `GET /api/sandbox/transactions` - List user's transactions
- Supports pagination and filtering
- Real-time status updates

**Transaction Details:**
- MTN Reference IDs
- Payer information
- Status tracking (PENDING, SUCCESSFUL, FAILED)
- Failure reasons when applicable
- Complete transaction metadata

## 🔧 API Endpoints

### Wallet Management
```
GET /api/sandbox/wallet
- Returns: wallet balance, currency, recent transactions
- Authentication: Required (student auth)
```

### API Key Management
```
GET /api/sandbox/api-keys
- Returns: list of user's API keys (without key values)
- Authentication: Required (student auth)

POST /api/sandbox/api-keys
- Body: { keyName: string }
- Returns: new API key (shown only once)
- Authentication: Required (student auth)
```

### Transaction Management
```
GET /api/sandbox/transactions
- Query params: limit, offset
- Returns: paginated transaction list
- Authentication: Required (student auth)
```

## 🛠️ Technical Implementation

### Database Models Used
- `SandboxWallet` - User wallet balances
- `SandboxApiKey` - API key management
- `SandboxTransaction` - Transaction records

### Authentication
- All endpoints protected with `withStudentAuth`
- Users can only access their own data
- Proper error handling for unauthorized access

### Data Flow
1. **Frontend** makes API calls to Next.js API routes
2. **API Routes** authenticate user and query Prisma database
3. **Database** returns real data (no mock data)
4. **Frontend** displays data with proper loading/error states

## 🎯 Usage Guide

### For Students

1. **Getting Started**
   - Navigate to `/sandbox/dashboard`
   - View your current wallet balance and statistics
   - Create your first API key in the API Access section

2. **Creating API Keys**
   - Go to API Access page
   - Click "Create API Key"
   - Enter a descriptive name
   - Copy the key immediately (won't be shown again)

3. **Monitoring Transactions**
   - Use the Transactions page to view all your sandbox activity
   - Filter by status or search by transaction ID
   - Click on transactions to view detailed information

4. **Wallet Management**
   - Check your balance on the Wallet page
   - View transaction history
   - Use "Refresh Data" to update information

### For Developers

1. **Integration Testing**
   - Use created API keys to test MTN MoMo integration
   - Monitor transactions in real-time
   - Debug using detailed transaction information

2. **API Development**
   - All sandbox transactions are tracked in the database
   - Use the dashboard to verify your API calls
   - Check transaction status and failure reasons

## 🔒 Security Features

- **API Key Hashing**: Keys are hashed in database using bcrypt
- **Usage Tracking**: Monitor API key usage and rate limits
- **User Isolation**: Users can only see their own data
- **Secure Display**: API keys masked in UI, shown only during creation

## 🚨 Error Handling

### Common Error Scenarios
- **Network Issues**: Retry buttons available
- **Authentication Failures**: Clear error messages
- **Data Loading Failures**: Graceful fallbacks
- **API Key Creation Errors**: Validation feedback

### Error Recovery
- All pages have retry functionality
- Clear error messages with actionable steps
- Fallback to safe defaults when possible

## 📈 Next Steps

### Recommended Enhancements
1. **Complete MTN MoMo Integration**: Add collect-payment endpoint
2. **Real-time Updates**: WebSocket integration for live transaction updates
3. **Advanced Filtering**: Date ranges, amount filters
4. **Export Functionality**: Download transaction reports
5. **Webhook Testing**: Test callback URLs in sandbox

### Development Priorities
1. Implement missing payment collection endpoint
2. Add comprehensive testing suite
3. Set up monitoring and logging
4. Add rate limiting and security headers

## 🔗 Related Documentation

- [Sandbox Setup Guide](./sandbox-setup-guide.md)
- [MTN MoMo Integration](./sandbox-implementation-plan.md)
- [API Documentation](./api-documentation.md)
- [Database Schema](./starterpay_data_model.md)

---

**Last Updated**: December 2024  
**Version**: 2.0 (Real Data Integration)
