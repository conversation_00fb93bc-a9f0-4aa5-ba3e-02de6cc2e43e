# StarterPay Sandbox Implementation Plan

## 🎯 **Goal: Collections API Only**
Focus on MTN MoMo Collections API (Request to Pay) for students to collect payments in their applications.

## 🏗️ **Architecture Flow**
```
Student App → StarterPay API → MTN MoMo Sandbox → StarterPay DB → Response
```

**Example:**
1. Student's e-commerce app calls: `POST /api/starterpay/collect-payment`
2. StarterPay calls MTN MoMo: `POST /collection/v1_0/requesttopay`
3. StarterPay stores transaction in database
4. StarterPay returns transaction ID to student
5. Student checks status: `GET /api/starterpay/transaction/{id}`

## 📋 **MTN MoMo API Flow (5 Steps)**

### **Step 1: Create API User**
- **Endpoint:** `POST /v1_0/apiuser`
- **Purpose:** Create user ID for sandbox
- **Headers:** `X-Reference-Id`, `Ocp-Apim-Subscription-Key`
- **Result:** User ID (same as X-Reference-Id)

### **Step 2: Create API Key**
- **Endpoint:** `POST /v1_0/apiuser/{userId}/apikey`
- **Purpose:** Generate API key for authentication
- **Headers:** `Ocp-Apim-Subscription-Key`
- **Result:** API Key (used as password)

### **Step 3: Create Bearer Token**
- **Endpoint:** `POST /collection/token/`
- **Purpose:** Get access token (expires in 1 hour)
- **Auth:** Basic Auth (User ID + API Key)
- **Headers:** `Ocp-Apim-Subscription-Key`, `X-Target-Environment: sandbox`
- **Result:** Bearer Token

### **Step 4: Request to Pay**
- **Endpoint:** `POST /collection/v1_0/requesttopay`
- **Purpose:** Initiate payment collection
- **Auth:** Bearer Token
- **Headers:** `X-Reference-Id` (transaction ID), `Ocp-Apim-Subscription-Key`, `X-Target-Environment`
- **Body:** Amount, currency (XAF), payer phone, messages
- **Result:** 202 Accepted

### **Step 5: Get Transaction Status**
- **Endpoint:** `GET /collection/v1_0/requesttopay/{transactionId}`
- **Purpose:** Check payment status
- **Auth:** Bearer Token
- **Headers:** Same as Step 4
- **Result:** Transaction details with status (SUCCESSFUL/FAILED/PENDING)

## 🗄️ **Database Models (✅ Created)**

### **SandboxWallet**
- Virtual balance per user (default: 100,000 XAF)
- Currency tracking
- Transaction history

### **SandboxApiKey**
- User-generated API keys for testing
- Usage tracking and rate limiting
- Active/inactive status

### **SandboxTransaction**
- Collections transactions only
- MTN MoMo integration fields
- Status tracking (PENDING/SUCCESSFUL/FAILED)
- Payer information and metadata

## 🚀 **Implementation Phases**

### **Phase 1: Database Setup** ✅
- [x] Create Prisma models
- [ ] Run database migration
- [ ] Seed initial data

### **Phase 2: MTN MoMo Service Layer**
- [ ] Create MTN MoMo API client
- [ ] Implement authentication flow
- [ ] Handle token management (auto-refresh)
- [ ] Create request-to-pay functionality
- [ ] Add transaction status checking

### **Phase 3: StarterPay API Endpoints**
- [ ] `/api/sandbox/wallet` - Get balance, add virtual funds
- [ ] `/api/sandbox/api-keys` - Generate, list, delete keys
- [ ] `/api/sandbox/collect-payment` - Main collection endpoint
- [ ] `/api/sandbox/transactions` - List and get transaction details

### **Phase 4: Frontend Integration**
- [ ] Replace mock data in sandbox dashboard
- [ ] Add real transaction forms
- [ ] Implement real-time status updates
- [ ] Add API key management UI

## 🔧 **Next Steps**

1. **Run Database Migration**
   ```bash
   npx prisma db push
   npx prisma generate
   ```

2. **Create MTN MoMo Service**
   - Authentication management
   - Request-to-pay implementation
   - Error handling

3. **Build API Endpoints**
   - Student-facing collection API
   - Sandbox management endpoints

4. **Update Frontend**
   - Replace mock data
   - Add real functionality

## 📝 **Key Requirements**

- **Currency:** XAF (Central African Franc) for Cameroon
- **Environment:** Sandbox only (no production access yet)
- **Authentication:** Bearer tokens (1-hour expiry)
- **Rate Limiting:** 100 requests per hour per API key
- **Phone Format:** International format (e.g., 237XXXXXXXX)

## 🎯 **Success Criteria**

1. Students can generate sandbox API keys
2. Students can integrate collection API in their apps
3. Real MTN MoMo sandbox transactions work
4. Transaction status tracking functions
5. Virtual wallet balance updates correctly
6. Dashboard shows real transaction data

## 🔗 **Resources**

- [MTN MoMo Developer Portal](https://momodeveloper.mtn.com/)
- [Detailed API Guide](https://gist.github.com/chaiwa-berian/5294fdf1360247cf4561c95c8fa740d4)
- [Testing Documentation](https://momodeveloper.mtn.com/api-documentation/testing/)
