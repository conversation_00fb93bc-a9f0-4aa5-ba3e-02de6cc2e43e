import Image from "next/image"

export function Testimonials() {
  const testimonials = [
    {
      quote:
        "StarterPay allowed me to integrate MTN MoMo payments into my final year project without the hassle of business registration. Game changer!",
      author: "<PERSON><PERSON><PERSON>",
      role: "Computer Science Student",
      university: "University of Yaoundé",
      avatar: "/placeholder.svg?height=60&width=60&text=JK",
    },
    {
      quote:
        "I was able to build and launch my campus delivery app in just two weeks thanks to StarterPay's simple API and great documentation.",
      author: "<PERSON>",
      role: "Software Engineering Student",
      university: "University of Douala",
      avatar: "/placeholder.svg?height=60&width=60&text=MN",
    },
    {
      quote:
        "The verification process was quick and the support team was incredibly helpful when I had questions about the API integration.",
      author: "<PERSON>",
      role: "IT Student",
      university: "University of Buea",
      avatar: "/placeholder.svg?height=60&width=60&text=ET",
    },
  ]

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-primary-600 text-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-full bg-white/20 px-3 py-1 text-sm font-medium text-white">
              Success Stories
            </div>
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">What Students Are Saying</h2>
            <p className="mx-auto max-w-[700px] text-primary-100 md:text-xl/relaxed">
              Hear from Cameroonian students who have built amazing projects with StarterPay.
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-3 mt-12">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="flex flex-col rounded-xl bg-white/10 backdrop-blur-sm p-6 shadow-lg">
              <div className="flex-1">
                <svg
                  className="h-8 w-8 text-accent mb-4 opacity-80"
                  fill="currentColor"
                  viewBox="0 0 32 32"
                  aria-hidden="true"
                >
                  <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                </svg>
                <p className="text-sm leading-relaxed mb-4">{testimonial.quote}</p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-full overflow-hidden bg-primary-200">
                  <Image
                    src={testimonial.avatar || "/placeholder.svg"}
                    alt={testimonial.author}
                    width={40}
                    height={40}
                    className="h-full w-full object-cover"
                  />
                </div>
                <div>
                  <p className="text-sm font-medium">{testimonial.author}</p>
                  <p className="text-xs text-primary-100">
                    {testimonial.role}, {testimonial.university}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
