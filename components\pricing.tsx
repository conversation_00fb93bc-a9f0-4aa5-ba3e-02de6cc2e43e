import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Check } from "lucide-react"

export function Pricing() {
  const tiers = [
    {
      name: "Student",
      description: "Perfect for student projects and academic work",
      price: "Free",
      features: [
        "Sandbox environment access",
        "100 transactions/month",
        "Basic API documentation",
        "Community support",
        "Student verification",
      ],
      cta: "Get Started",
      href: "/register",
      highlighted: false,
    },
    {
      name: "Graduate",
      description: "For recent graduates building startups",
      price: "5,000 FCFA",
      period: "/month",
      features: [
        "Production environment access",
        "500 transactions/month",
        "Advanced API documentation",
        "Email support",
        "Webhook notifications",
        "Transaction analytics",
      ],
      cta: "Start Free Trial",
      href: "/register",
      highlighted: true,
    },
    {
      name: "Startup",
      description: "For established student startups",
      price: "15,000 FCFA",
      period: "/month",
      features: [
        "Production environment access",
        "2,000 transactions/month",
        "Complete API documentation",
        "Priority support",
        "Advanced analytics",
        "Custom integration support",
        "Dedicated account manager",
      ],
      cta: "Contact Sales",
      href: "/contact",
      highlighted: false,
    },
  ]

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-primary-50 to-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
          <div className="space-y-2">
            <div className="inline-block rounded-full bg-primary-100 px-3 py-1 text-sm font-medium text-primary-700">
              Pricing
            </div>
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Simple, Transparent Pricing</h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed">
              Start for free and scale as your projects grow from classroom to market.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3 lg:gap-8">
          {tiers.map((tier, index) => (
            <div
              key={index}
              className={`flex flex-col rounded-xl border ${
                tier.highlighted
                  ? "border-primary-200 bg-gradient-to-b from-primary-50 to-white shadow-lg"
                  : "border-gray-200 bg-white"
              } p-6 shadow-sm`}
            >
              {tier.highlighted && (
                <div className="absolute -top-4 left-0 right-0 mx-auto w-fit rounded-full bg-primary-600 px-3 py-1 text-xs font-medium text-white">
                  Most Popular
                </div>
              )}
              <div className="mb-4">
                <h3 className="text-lg font-bold">{tier.name}</h3>
                <p className="text-sm text-gray-500">{tier.description}</p>
              </div>
              <div className="mb-6">
                <span className="text-3xl font-bold">{tier.price}</span>
                {tier.period && <span className="text-gray-500">{tier.period}</span>}
              </div>
              <ul className="mb-6 space-y-2 flex-1">
                {tier.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-sm">
                    <Check className="mr-2 h-4 w-4 text-primary-600" />
                    {feature}
                  </li>
                ))}
              </ul>
              <Button
                asChild
                className={
                  tier.highlighted
                    ? "bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800"
                    : ""
                }
                variant={tier.highlighted ? "default" : "outline"}
              >
                <Link href={tier.href}>{tier.cta}</Link>
              </Button>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-sm text-gray-500">
            Need a custom plan for your university or student organization?{" "}
            <Link href="/contact" className="text-primary-600 hover:underline font-medium">
              Contact us
            </Link>
          </p>
        </div>
      </div>
    </section>
  )
}
