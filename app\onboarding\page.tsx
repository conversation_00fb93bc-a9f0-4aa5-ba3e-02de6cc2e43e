"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { Header } from "@/components/header"

// Schema for each step
const step1Schema = z.object({
  universityName: z.string().min(1, "University name is required"),
  faculty: z.string().min(1, "Faculty/Department is required"),
  programOfStudy: z.string().min(1, "Program of study is required"),
  graduationYear: z.string().min(1, "Expected graduation year is required"),
  schoolAddress: z.string().optional(),
  schoolContact: z.string().optional(),
  schoolWebsite: z.string().optional(),
})

const step2Schema = z.object({
  studentIdCard: z.any().refine((file) => file, "Student ID card is required"),
  registrationReceipt: z.any().refine((file) => file, "Registration receipt is required"),
  passportPhoto: z.any().refine((file) => file, "Passport photo is required"),
  nationalId: z.any().optional(),
})

const step3Schema = z.object({
  apiPurpose: z.string().min(1, "Please select what you'll use the API for"),
  projectName: z.string().optional(),
  projectDescription: z.string().min(10, "Please provide a brief project description"),
  githubLink: z.string().optional(),
})

const step4Schema = z.object({
  studentIdNumber: z.string().min(1, "Student ID number is required"),
  yearOfStudy: z.string().min(1, "Level/Year of study is required"),
  preferredLanguage: z.string().optional(),
  termsAccepted: z.boolean().refine((val) => val === true, "You must accept the terms"),
})

export default function OnboardingPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({})
  const [files, setFiles] = useState<Record<string, File>>({})

  const step1Form = useForm<z.infer<typeof step1Schema>>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      universityName: "",
      faculty: "",
      programOfStudy: "",
      graduationYear: "",
      schoolAddress: "",
      schoolContact: "",
      schoolWebsite: "",
    },
  })

  const step3Form = useForm<z.infer<typeof step3Schema>>({
    resolver: zodResolver(step3Schema),
    defaultValues: {
      apiPurpose: "",
      projectName: "",
      projectDescription: "",
      githubLink: "",
    },
  })

  const step4Form = useForm<z.infer<typeof step4Schema>>({
    resolver: zodResolver(step4Schema),
    defaultValues: {
      studentIdNumber: "",
      yearOfStudy: "",
      preferredLanguage: "",
      termsAccepted: false,
    },
  })

  const handleFileUpload = (fieldName: string, file: File | null) => {
    if (file) {
      // Validate file type and size
      const validTypes = ["image/jpeg", "image/png", "application/pdf"]
      if (!validTypes.includes(file.type)) {
        alert("Please upload a JPG, PNG, or PDF file")
        return
      }
      if (file.size > 2 * 1024 * 1024) {
        alert("File size must be less than 2MB")
        return
      }
      setFiles((prev) => ({ ...prev, [fieldName]: file }))
    }
  }

  const onStep1Submit = (values: z.infer<typeof step1Schema>) => {
    setFormData((prev) => ({ ...prev, ...values }))
    setCurrentStep(2)
  }

  const onStep2Submit = () => {
    // Validate required files
    if (!files.studentIdCard || !files.registrationReceipt || !files.passportPhoto) {
      alert("Please upload all required documents")
      return
    }
    setCurrentStep(3)
  }

  const onStep3Submit = (values: z.infer<typeof step3Schema>) => {
    setFormData((prev) => ({ ...prev, ...values }))
    setCurrentStep(4)
  }

  const onStep4Submit = (values: z.infer<typeof step4Schema>) => {
    setIsSubmitting(true)
    const finalData = { ...formData, ...values, files }

    // This would be replaced with actual API call
    setTimeout(() => {
      console.log("Final submission:", finalData)
      setIsSubmitting(false)
      setCurrentStep(5) // Success step
    }, 2000)
  }

  const goBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const progressPercentage = (currentStep / 4) * 100

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 py-12 bg-gradient-to-b from-white to-primary-50">
        <div className="container max-w-2xl px-4 md:px-6">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <h1 className="text-2xl font-bold">Student Verification</h1>
              <span className="text-sm text-gray-500">Step {currentStep} of 4</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          <Card className="border-none shadow-lg">
            {/* Step 1: School Information */}
            {currentStep === 1 && (
              <>
                <CardHeader>
                  <CardTitle>School Information</CardTitle>
                  <CardDescription>Tell us about your educational institution</CardDescription>
                </CardHeader>
                <Form {...step1Form}>
                  <form onSubmit={step1Form.handleSubmit(onStep1Submit)}>
                    <CardContent className="space-y-4">
                      <FormField
                        control={step1Form.control}
                        name="universityName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>University / Institution Name *</FormLabel>
                            <FormControl>
                              <Input placeholder="University of Yaoundé" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step1Form.control}
                        name="faculty"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Faculty / Department *</FormLabel>
                            <FormControl>
                              <Input placeholder="Faculty of Science" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step1Form.control}
                        name="programOfStudy"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Program of Study *</FormLabel>
                            <FormControl>
                              <Input placeholder="Computer Science" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step1Form.control}
                        name="graduationYear"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Expected Graduation Year *</FormLabel>
                            <FormControl>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select year" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="2024">2024</SelectItem>
                                  <SelectItem value="2025">2025</SelectItem>
                                  <SelectItem value="2026">2026</SelectItem>
                                  <SelectItem value="2027">2027</SelectItem>
                                  <SelectItem value="2028">2028</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step1Form.control}
                        name="schoolAddress"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Address</FormLabel>
                            <FormControl>
                              <Input placeholder="Optional" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step1Form.control}
                        name="schoolContact"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Contact Number</FormLabel>
                            <FormControl>
                              <Input placeholder="Optional" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step1Form.control}
                        name="schoolWebsite"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>School Website</FormLabel>
                            <FormControl>
                              <Input placeholder="Optional" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                    <div className="flex justify-end p-6">
                      <Button type="submit" className="bg-gradient-to-r from-primary-600 to-primary-700">
                        Next
                      </Button>
                    </div>
                  </form>
                </Form>
              </>
            )}

            {/* Step 2: Identity Verification */}
            {currentStep === 2 && (
              <>
                <CardHeader>
                  <CardTitle>Identity Verification</CardTitle>
                  <CardDescription>Upload your identification documents</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Student ID Card *</label>
                      <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-4">
                        <input
                          type="file"
                          accept=".jpg,.jpeg,.png,.pdf"
                          onChange={(e) => handleFileUpload("studentIdCard", e.target.files?.[0] || null)}
                          className="w-full"
                        />
                        {files.studentIdCard && (
                          <p className="text-sm text-green-600 mt-2">✓ {files.studentIdCard.name}</p>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Registration Receipt *</label>
                      <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-4">
                        <input
                          type="file"
                          accept=".jpg,.jpeg,.png,.pdf"
                          onChange={(e) => handleFileUpload("registrationReceipt", e.target.files?.[0] || null)}
                          className="w-full"
                        />
                        {files.registrationReceipt && (
                          <p className="text-sm text-green-600 mt-2">✓ {files.registrationReceipt.name}</p>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Passport Photo *</label>
                      <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-4">
                        <input
                          type="file"
                          accept=".jpg,.jpeg,.png"
                          onChange={(e) => handleFileUpload("passportPhoto", e.target.files?.[0] || null)}
                          className="w-full"
                        />
                        {files.passportPhoto && (
                          <p className="text-sm text-green-600 mt-2">✓ {files.passportPhoto.name}</p>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium">National ID Card</label>
                      <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-4">
                        <input
                          type="file"
                          accept=".jpg,.jpeg,.png,.pdf"
                          onChange={(e) => handleFileUpload("nationalId", e.target.files?.[0] || null)}
                          className="w-full"
                        />
                        {files.nationalId && <p className="text-sm text-green-600 mt-2">✓ {files.nationalId.name}</p>}
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500">Accepted formats: JPG, PNG, PDF. Maximum file size: 2MB</p>
                </CardContent>
                <div className="flex justify-between p-6">
                  <Button variant="outline" onClick={goBack}>
                    Back
                  </Button>
                  <Button onClick={onStep2Submit} className="bg-gradient-to-r from-primary-600 to-primary-700">
                    Next
                  </Button>
                </div>
              </>
            )}

            {/* Step 3: Project Purpose */}
            {currentStep === 3 && (
              <>
                <CardHeader>
                  <CardTitle>Project Purpose</CardTitle>
                  <CardDescription>Tell us about your project</CardDescription>
                </CardHeader>
                <Form {...step3Form}>
                  <form onSubmit={step3Form.handleSubmit(onStep3Submit)}>
                    <CardContent className="space-y-4">
                      <FormField
                        control={step3Form.control}
                        name="apiPurpose"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>What will you use the API for? *</FormLabel>
                            <FormControl>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select purpose" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="academic">Academic Project</SelectItem>
                                  <SelectItem value="hackathon">Hackathon</SelectItem>
                                  <SelectItem value="startup">Startup MVP</SelectItem>
                                  <SelectItem value="learning">Personal Learning</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step3Form.control}
                        name="projectName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Project Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Optional" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step3Form.control}
                        name="projectDescription"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Brief Project Description *</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Describe your project and how you plan to use the MTN MoMo API"
                                className="min-h-[100px]"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step3Form.control}
                        name="githubLink"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>GitHub Link or Website</FormLabel>
                            <FormControl>
                              <Input placeholder="Optional" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                    <div className="flex justify-between p-6">
                      <Button variant="outline" onClick={goBack}>
                        Back
                      </Button>
                      <Button type="submit" className="bg-gradient-to-r from-primary-600 to-primary-700">
                        Next
                      </Button>
                    </div>
                  </form>
                </Form>
              </>
            )}

            {/* Step 4: Student Profile Info */}
            {currentStep === 4 && (
              <>
                <CardHeader>
                  <CardTitle>Student Profile</CardTitle>
                  <CardDescription>Complete your student profile</CardDescription>
                </CardHeader>
                <Form {...step4Form}>
                  <form onSubmit={step4Form.handleSubmit(onStep4Submit)}>
                    <CardContent className="space-y-4">
                      <FormField
                        control={step4Form.control}
                        name="studentIdNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Student ID Number *</FormLabel>
                            <FormControl>
                              <Input placeholder="S12345" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step4Form.control}
                        name="yearOfStudy"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Level / Year of Study *</FormLabel>
                            <FormControl>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select level" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="100">100 Level</SelectItem>
                                  <SelectItem value="200">200 Level</SelectItem>
                                  <SelectItem value="300">300 Level</SelectItem>
                                  <SelectItem value="400">400 Level</SelectItem>
                                  <SelectItem value="500">500 Level</SelectItem>
                                  <SelectItem value="final">Final Year</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step4Form.control}
                        name="preferredLanguage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Preferred Language</FormLabel>
                            <FormControl>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Optional" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="english">English</SelectItem>
                                  <SelectItem value="french">French</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={step4Form.control}
                        name="termsAccepted"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>I confirm this project is for academic/testing purposes only *</FormLabel>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                    <div className="flex justify-between p-6">
                      <Button variant="outline" onClick={goBack}>
                        Back
                      </Button>
                      <Button
                        type="submit"
                        className="bg-gradient-to-r from-primary-600 to-primary-700"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? "Submitting..." : "Submit"}
                      </Button>
                    </div>
                  </form>
                </Form>
              </>
            )}

            {/* Step 5: Success - REMOVED DASHBOARD BUTTON */}
            {currentStep === 5 && (
              <>
                <CardHeader>
                  <CardTitle>Verification Submitted!</CardTitle>
                  <CardDescription>Your KYC verification has been submitted successfully</CardDescription>
                </CardHeader>
                <CardContent className="text-center py-8">
                  <div className="rounded-full bg-success p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-8 w-8 text-white"
                    >
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">Thank you!</h3>
                  <p className="text-gray-600 mb-6">
                    Your verification documents have been submitted. Our team will review your application and notify
                    you once approved.
                  </p>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <p className="text-sm text-yellow-800">
                      <strong>Status:</strong> Pending Review
                    </p>
                    <p className="text-sm text-yellow-800 mt-1">
                      You will receive an email notification with login instructions once your verification is complete.
                    </p>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p className="text-sm text-blue-800">
                      <strong>What's Next?</strong>
                    </p>
                    <p className="text-sm text-blue-800 mt-1">
                      Our admin team will review your documents within 24-48 hours. Once approved, you'll receive an
                      email with your dashboard access link.
                    </p>
                  </div>
                </CardContent>
              </>
            )}
          </Card>
        </div>
      </main>
    </div>
  )
}
