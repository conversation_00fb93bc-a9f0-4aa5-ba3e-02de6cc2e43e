import { NextRequest, NextResponse } from 'next/server'
import { SandboxService, SandboxError } from '@/lib/sandbox-service'

/**
 * GET /api/sandbox/transactions/{id}
 * Get transaction details and status
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get API key from Authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const apiKey = authHeader.substring(7) // Remove "Bearer "
    
    // Validate API key
    const sandboxService = new SandboxService()
    const keyValidation = await sandboxService.validateApiKey(apiKey)
    
    if (!keyValidation) {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401 }
      )
    }

    // Get transaction
    const transaction = await sandboxService.getTransaction(
      params.id,
      keyValidation.userId
    )

    return NextResponse.json({
      success: true,
      data: {
        id: transaction.id,
        externalId: transaction.externalId,
        amount: Number(transaction.amount),
        currency: transaction.currency,
        status: transaction.status,
        payerPhone: transaction.payerPhone,
        payerMessage: transaction.payerMessage,
        payeeNote: transaction.payeeNote,
        createdAt: transaction.createdAt,
        completedAt: transaction.completedAt,
        failureReason: transaction.failureReason
      }
    })

  } catch (error) {
    console.error('Get transaction error:', error)

    if (error instanceof SandboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code
        },
        { status: error.statusCode }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
