import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export function CTA() {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              Your Innovation Journey Starts Here
            </h2>
            <p className="mx-auto max-w-[700px] text-primary-100 md:text-xl/relaxed">
              Join the community of Cameroonian student innovators building the future of digital payments.
            </p>
          </div>
          <div className="flex flex-col gap-3 min-[400px]:flex-row">
            <Button size="lg" asChild className="bg-white text-primary-700 hover:bg-primary-50 rounded-full px-8">
              <Link href="/register">Create Free Account</Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              asChild
              className="rounded-full px-8 border-white text-white hover:bg-white/10"
            >
              <Link href="/docs">Explore Documentation</Link>
            </Button>
          </div>
          <div className="mt-6">
            <p className="text-primary-100">
              "The future belongs to those who believe in the beauty of their dreams." — Eleanor Roosevelt
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
