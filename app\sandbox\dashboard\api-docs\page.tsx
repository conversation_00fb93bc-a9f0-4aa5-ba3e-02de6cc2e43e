"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Copy, Check, ExternalLink, Book, Code, FileText } from "lucide-react"

export default function ApiDocsPage() {
  const [copied, setCopied] = useState(false)

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold">API Documentation</h1>
        <p className="text-gray-500">Learn how to integrate with MTN MoMo APIs</p>
      </div>

      <div className="mb-6 rounded-lg bg-blue-50 border border-blue-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Book className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-blue-800">🧪 Sandbox API Documentation</p>
            <p className="text-sm text-blue-700 mt-1">
              This documentation is for the sandbox environment only. Use these endpoints for testing and development.
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="overview">
        <TabsList className="mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="request-to-pay">Request to Pay</TabsTrigger>
          <TabsTrigger value="status-check">Status Check</TabsTrigger>
          <TabsTrigger value="code-examples">Code Examples</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Getting Started</CardTitle>
                <CardDescription>Essential information for MTN MoMo integration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Base URL</h3>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 p-3 bg-gray-100 rounded-md text-sm font-mono overflow-auto">
                      https://sandbox-api.starterpay.cm/v1
                    </code>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => copyToClipboard("https://sandbox-api.starterpay.cm/v1")}
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Authentication</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    All API requests require authentication using your API key in the <code>Authorization</code> header:
                  </p>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 p-3 bg-gray-100 rounded-md text-sm font-mono overflow-auto">
                      Authorization: Bearer sk_test_sandbox_your_api_key
                    </code>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() =>
                        copyToClipboard("Authorization: Bearer sk_test_sandbox_your_api_key")
                      }
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Content Type</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    All requests must include the <code>Content-Type</code> header:
                  </p>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 p-3 bg-gray-100 rounded-md text-sm font-mono overflow-auto">
                      Content-Type: application/json
                    </code>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => copyToClipboard("Content-Type: application/json")}
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Available Endpoints</CardTitle>
                <CardDescription>MTN MoMo API endpoints for sandbox testing</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Collections API</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Used to request payments from customers to your business.
                  </p>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium">Request to Pay</p>
                      <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono">
                        POST /collections/request-to-pay
                      </code>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Check Payment Status</p>
                      <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono">
                        GET /collections/{"{transactionId}"}/status
                      </code>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Disbursements API</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Used to send payments from your business to customers.
                  </p>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium">Transfer</p>
                      <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono">
                        POST /disbursements/transfer
                      </code>
                      <Badge className="bg-yellow-100 text-yellow-800 mt-2">Coming Soon</Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Check Transfer Status</p>
                      <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono">
                        GET /disbursements/{"{transactionId}"}/status
                      </code>
                      <Badge className="bg-yellow-100 text-yellow-800 mt-2">Coming Soon</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Test Credentials</CardTitle>
                <CardDescription>Use these test numbers for your sandbox transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Test Phone Numbers</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="p-4 border rounded-md">
                        <p className="font-mono text-lg font-semibold">+************</p>
                        <p className="text-sm text-gray-500">Success response</p>
                      </div>
                      <div className="p-4 border rounded-md">
                        <p className="font-mono text-lg font-semibold">+237623456789</p>
                        <p className="text-sm text-gray-500">Failed response</p>
                      </div>
                      <div className="p-4 border rounded-md">
                        <p className="font-mono text-lg font-semibold">+237634567890</p>
                        <p className="text-sm text-gray-500">Pending response</p>
                      </div>
                      <div className="p-4 border rounded-md">
                        <p className="font-mono text-lg font-semibold">+237645678901</p>
                        <p className="text-sm text-gray-500">Timeout response</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Amount Ranges</h3>
                    <div className="grid grid-cols-1 gap-4">
                      <div className="p-4 border rounded-md">
                        <p className="font-semibold">Minimum: 100 XAF</p>
                        <p className="text-sm text-gray-500">Smallest amount allowed</p>
                      </div>
                      <div className="p-4 border rounded-md">
                        <p className="font-semibold">Maximum: 1,000,000 XAF</p>
                        <p className="text-sm text-gray-500">Largest amount allowed per transaction</p>
                      </div>
                      <div className="p-4 border rounded-md flex items-center justify-between">
                        <div>
                          <p className="font-semibold">Special Amount: 999</p>
                          <p className="text-sm text-gray-500">Triggers a test failure</p>
                        </div>
                        <div>
                          <Badge className="bg-destructive text-white">Test Only</Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="request-to-pay">
          <Card>
            <CardHeader>
              <CardTitle>Request to Pay API</CardTitle>
              <CardDescription>Request payment from a customer to your business</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Endpoint</h3>
                <div className="flex items-center space-x-2">
                  <code className="flex-1 p-3 bg-gray-100 rounded-md text-sm font-mono overflow-auto">
                    POST https://sandbox-api.starterpay.cm/v1/collections/request-to-pay
                  </code>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() =>
                      copyToClipboard("POST https://sandbox-api.starterpay.cm/v1/collections/request-to-pay")
                    }
                  >
                    {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Request Headers</h3>
                <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                  <pre>
                    {`Authorization: Bearer sk_test_sandbox_your_api_key
Content-Type: application/json
X-Reference-Id: UUID_for_transaction
X-Target-Environment: sandbox`}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Request Body</h3>
                <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                  <pre>
                    {`{
  "amount": "1000",
  "currency": "XAF",
  "externalId": "unique_id_from_your_system",
  "payer": {
    "partyIdType": "MSISDN",
    "partyId": "+************"
  },
  "payerMessage": "Payment for product/service",
  "payeeNote": "Order #12345"
}`}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Success Response</h3>
                <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                  <pre>
                    {`// Status Code: 202 Accepted

{
  "transactionId": "txn_1234567890abcdef",
  "status": "PENDING",
  "reason": {
    "code": "PENDING_CONFIRMATION",
    "message": "Transaction is pending confirmation from the payer"
  }
}`}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Error Response</h3>
                <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                  <pre>
                    {`// Status Code: 400 Bad Request

{
  "status": "FAILED",
  "reason": {
    "code": "PAYER_NOT_FOUND",
    "message": "The payer account was not found"
  }
}`}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Parameters</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Parameter
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Required
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200 text-sm">
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">amount</td>
                        <td className="px-6 py-4 whitespace-nowrap">String</td>
                        <td className="px-6 py-4 whitespace-nowrap">Yes</td>
                        <td className="px-6 py-4">The amount to request (min: 100 XAF)</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">currency</td>
                        <td className="px-6 py-4 whitespace-nowrap">String</td>
                        <td className="px-6 py-4 whitespace-nowrap">Yes</td>
                        <td className="px-6 py-4">The currency (XAF only for Cameroon)</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">externalId</td>
                        <td className="px-6 py-4 whitespace-nowrap">String</td>
                        <td className="px-6 py-4 whitespace-nowrap">Yes</td>
                        <td className="px-6 py-4">Your system reference for this transaction</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">payer.partyIdType</td>
                        <td className="px-6 py-4 whitespace-nowrap">String</td>
                        <td className="px-6 py-4 whitespace-nowrap">Yes</td>
                        <td className="px-6 py-4">Type of payer identifier (always MSISDN)</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">payer.partyId</td>
                        <td className="px-6 py-4 whitespace-nowrap">String</td>
                        <td className="px-6 py-4 whitespace-nowrap">Yes</td>
                        <td className="px-6 py-4">Payer's phone number with country code</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">payerMessage</td>
                        <td className="px-6 py-4 whitespace-nowrap">String</td>
                        <td className="px-6 py-4 whitespace-nowrap">No</td>
                        <td className="px-6 py-4">Message shown to the payer</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">payeeNote</td>
                        <td className="px-6 py-4 whitespace-nowrap">String</td>
                        <td className="px-6 py-4 whitespace-nowrap">No</td>
                        <td className="px-6 py-4">Note for your reference</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="status-check">
          <Card>
            <CardHeader>
              <CardTitle>Payment Status Check API</CardTitle>
              <CardDescription>Check the status of a payment request</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Endpoint</h3>
                <div className="flex items-center space-x-2">
                  <code className="flex-1 p-3 bg-gray-100 rounded-md text-sm font-mono overflow-auto">
                    GET https://sandbox-api.starterpay.cm/v1/collections/{"{transactionId}"}/status
                  </code>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() =>
                      copyToClipboard("GET https://sandbox-api.starterpay.cm/v1/collections/{transactionId}/status")
                    }
                  >
                    {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Request Headers</h3>
                <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                  <pre>
                    {`Authorization: Bearer sk_test_sandbox_your_api_key
X-Target-Environment: sandbox`}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Path Parameters</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Parameter
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Required
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200 text-sm">
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">transactionId</td>
                        <td className="px-6 py-4 whitespace-nowrap">String</td>
                        <td className="px-6 py-4 whitespace-nowrap">Yes</td>
                        <td className="px-6 py-4">The transaction ID returned from the request-to-pay call</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Success Response</h3>
                <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                  <pre>
                    {`// Status Code: 200 OK

{
  "transactionId": "txn_1234567890abcdef",
  "externalId": "unique_id_from_your_system",
  "amount": "1000",
  "currency": "XAF",
  "payer": {
    "partyIdType": "MSISDN",
    "partyId": "+************"
  },
  "payee": {
    "partyIdType": "MSISDN",
    "partyId": "+237600000000"
  },
  "status": "SUCCESSFUL",
  "reason": {
    "code": "PAYER_COMPLETED",
    "message": "The payer has completed the payment"
  },
  "requestTimestamp": "2025-06-15T14:30:00Z",
  "responseTimestamp": "2025-06-15T14:30:45Z"
}`}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Possible Status Values</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200 text-sm">
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">
                          <div>
                            <Badge className="bg-success text-white">SUCCESSFUL</Badge>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          The payment has been successfully processed and funds transferred
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">
                          <div>
                            <Badge className="bg-yellow-500 text-white">PENDING</Badge>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          The payment request is pending confirmation from the payer
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">
                          <div>
                            <Badge className="bg-destructive text-white">FAILED</Badge>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          The payment request has failed due to an error or rejection
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">
                          <div>
                            <Badge className="bg-gray-500 text-white">TIMEOUT</Badge>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          The payment request has timed out without a response
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="code-examples">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>JavaScript Example</CardTitle>
                    <CardDescription>Using Fetch API</CardDescription>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">Node.js</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Request to Pay</h3>
                    <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                      <pre>
                        {`const axios = require('axios');

// Generate a unique transaction ID
const transactionId = 'txn_example_123456789';

// Make the request-to-pay API call
async function requestToPay() {
  try {
    const response = await axios({
      method: 'post',
      url: 'https://sandbox-api.starterpay.cm/v1/collections/request-to-pay',
      headers: {
        'Authorization': 'Bearer sk_test_sandbox_your_api_key',
        'Content-Type': 'application/json',
        'X-Reference-Id': transactionId,
        'X-Target-Environment': 'sandbox'
      },
      data: {
        amount: '1000',
        currency: 'XAF',
        externalId: 'order_123',
        payer: {
          partyIdType: 'MSISDN',
          partyId: '+************'
        },
        payerMessage: 'Payment for Order #123',
        payeeNote: 'Order #123'
      }
    });
    
    console.log('Response:', response.data);
    return transactionId; // Return the transaction ID for status check
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}`}
                      </pre>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">Check Payment Status</h3>
                    <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                      <pre>
                        {`// Check the status of a payment
async function checkPaymentStatus(transactionId) {
  try {
    const response = await axios({
      method: 'get',
      url: \`https://sandbox-api.starterpay.cm/v1/collections/\${transactionId}/status\`,
      headers: {
        'Authorization': 'Bearer sk_test_sandbox_your_api_key',
        'X-Target-Environment': 'sandbox'
      }
    });
    
    console.log('Payment Status:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error:', error.response ? error.response.data : error.message);
  }
}

// Usage example
async function processPayment() {
  const txnId = await requestToPay();
  
  // Wait a bit for the payment to be processed
  console.log('Waiting for payment processing...');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // Check the payment status
  await checkPaymentStatus(txnId);
}`}
                      </pre>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Python Example</CardTitle>
                    <CardDescription>Using Requests Library</CardDescription>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800">Python</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Request to Pay</h3>
                    <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                      <pre>
                        {`import requests
import time
import random
import string

# Generate a unique transaction ID
transaction_id = 'txn_' + ''.join(random.choices(string.ascii_lowercase + string.digits, k=10)) + '_' + str(int(time.time()))

def request_to_pay():
    """
    Make a request-to-pay API call to the MTN MoMo API
    """
    url = "https://sandbox-api.starterpay.cm/v1/collections/request-to-pay"
    
    headers = {
        "Authorization": "Bearer sk_test_sandbox_your_api_key",
        "Content-Type": "application/json",
        "X-Reference-Id": transaction_id,
        "X-Target-Environment": "sandbox"
    }
    
    payload = {
        "amount": "1000",
        "currency": "XAF",
        "externalId": "order_123",
        "payer": {
            "partyIdType": "MSISDN",
            "partyId": "+************"
        },
        "payerMessage": "Payment for Order #123",
        "payeeNote": "Order #123"
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        print(f"Request successful: {response.status_code}")
        return transaction_id
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None`}
                      </pre>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">Check Payment Status</h3>
                    <div className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm font-mono">
                      <pre>
                        {`def check_payment_status(transaction_id):
    """
    Check the status of a payment request
    """
    url = f"https://sandbox-api.starterpay.cm/v1/collections/{transaction_id}/status"
    
    headers = {
        "Authorization": "Bearer sk_test_sandbox_your_api_key",
        "X-Target-Environment": "sandbox"
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        print(f"Payment status: {response.status_code}")
        print(response.json())
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

# Usage example
def process_payment():
    txn_id = request_to_pay()
    if txn_id:
        print("Waiting for payment processing...")
        time.sleep(5)  # Wait a bit for processing
        check_payment_status(txn_id)

# Run the example
if __name__ == "__main__":
    process_payment()`}
                      </pre>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6 grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>API Reference</CardTitle>
                <CardDescription>Complete API documentation</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col gap-4">
                  <Button variant="outline" className="flex justify-between items-center w-full" asChild>
                    <Link href="#" target="_blank">
                      <span className="flex items-center">
                        <FileText className="h-4 w-4 mr-2" />
                        Full API Reference
                      </span>
                      <ExternalLink className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button variant="outline" className="flex justify-between items-center w-full" asChild>
                    <Link href="#" target="_blank">
                      <span className="flex items-center">
                        <Code className="h-4 w-4 mr-2" />
                        More Code Examples
                      </span>
                      <ExternalLink className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Try it Yourself</CardTitle>
                <CardDescription>Test the API endpoints</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Use our API Testing page to try out these endpoints with a user-friendly interface.
                </p>
                <Button className="w-full bg-blue-600 hover:bg-blue-700" asChild>
                  <Link href="/sandbox/dashboard/api-testing">
                    Go to API Testing
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
                <CardDescription>Support resources</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  If you need assistance with API integration, we're here to help.
                </p>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="#" target="_blank">
                      Contact Support
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="#" target="_blank">
                      Join Developer Community
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}