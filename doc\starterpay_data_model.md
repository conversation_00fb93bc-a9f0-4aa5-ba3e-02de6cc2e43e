4# StarterPay Complete Data Model Design

## 1. Core Data Requirements Analysis

### What Data Does StarterPay Actually Need?

Before jumping into tables, let's identify ALL the data our system needs to handle:

**User Management Data:**
- User profiles (students, admins)
- Authentication credentials
- User roles and permissions
- Account status and verification state

**Verification/KYC Data:**
- Document uploads (student IDs, enrollment letters)
- Verification status tracking
- Admin review decisions
- Verification history/audit trail

**API Access Control Data:**
- API keys and their metadata
- Usage quotas and limits
- Rate limiting data
- Environment access permissions (sandbox/production)

**Financial Transaction Data:**
- Payment requests and responses
- Transaction status tracking
- Virtual wallet balances (sandbox)
- Real money transactions (production)
- Fee calculations and records

**System Monitoring Data:**
- API usage analytics
- Performance metrics
- Error logs and debugging info
- Audit trails for security

**Administrative Data:**
- System configurations
- Rate limiting rules
- Notification templates
- Support tickets/help requests

## 2. Data Model Structure (MVP vs Production)

### 🚀 MVP Phase 1 - Foundational Models (Required for Demo)
*These models are essential for the MVP to function and demonstrate value to <PERSON><PERSON>N*

#### 2.1 Users & Authentication (MVP Required)

```sql
-- Enable the commented models from MVP sections:
-- verification_history, transaction_status_history

-- Advanced rate limiting
CREATE TABLE rate_limit_violations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    api_key_id UUID REFERENCES api_keys(id),
    ip_address INET,
    endpoint VARCHAR(255),
    violation_count INTEGER DEFAULT 1,
    window_start TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Fine-grained API permissions
CREATE TABLE api_key_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
    permission_type ENUM('collections', 'disbursements', 'balance_check', 'webhook') NOT NULL,
    is_granted BOOLEAN DEFAULT TRUE,
    granted_at TIMESTAMP DEFAULT NOW()
);
```

#### 2.7 User Communication & Support

```sql
-- System notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    notification_type ENUM('verification', 'transaction', 'security', 'system') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT NOW(),
    read_at TIMESTAMP
);
-- Core user table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    student_id VARCHAR(100), -- Only for students
    institution VARCHAR(255), -- Only for students
    user_type ENUM('student', 'admin', 'super_admin') NOT NULL DEFAULT 'student',
    account_status ENUM('active', 'suspended', 'deactivated') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_login_at TIMESTAMP
);

-- Separate admin profiles for additional admin-specific data
CREATE TABLE admin_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    department VARCHAR(255),
    permissions JSONB, -- Flexible permissions system
    created_at TIMESTAMP DEFAULT NOW()
);

-- Student profiles for student-specific data
CREATE TABLE student_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    university VARCHAR(255) NOT NULL,
    program VARCHAR(255),
    graduation_year INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 2.2 Verification/KYC System (MVP Required)
*Essential for student verification workflow*

```sql
-- Verification requests (separate from users for better tracking)
CREATE TABLE verification_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status ENUM('pending', 'under_review', 'approved', 'rejected', 'expired') DEFAULT 'pending',
    verification_type ENUM('student_id', 'enrollment_letter', 'university_email') NOT NULL,
    submitted_at TIMESTAMP DEFAULT NOW(),
    reviewed_at TIMESTAMP,
    reviewed_by UUID REFERENCES users(id), -- Admin who reviewed
    review_notes TEXT,
    expires_at TIMESTAMP -- For time-limited verifications
);

-- Document storage
CREATE TABLE verification_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_request_id UUID REFERENCES verification_requests(id) ON DELETE CASCADE,
    document_type ENUM('student_id', 'enrollment_letter', 'passport', 'other') NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    upload_date TIMESTAMP DEFAULT NOW()
);

-- Verification history for audit trail (PHASE 2)
-- CREATE TABLE verification_history (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     verification_request_id UUID REFERENCES verification_requests(id) ON DELETE CASCADE,
--     old_status VARCHAR(50),
--     new_status VARCHAR(50),
--     changed_by UUID REFERENCES users(id),
--     reason TEXT,
--     changed_at TIMESTAMP DEFAULT NOW()
-- );
```

#### 2.3 API Key Management (MVP Required)

```sql
-- API keys with better metadata
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    key_name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL, -- Hashed version of the key
    key_prefix VARCHAR(20) NOT NULL, -- First few chars for identification
    environment ENUM('sandbox', 'production') DEFAULT 'sandbox',
    status ENUM('active', 'revoked', 'expired') DEFAULT 'active',
    
    -- Usage controls
    usage_quota INTEGER DEFAULT 1000,
    usage_count INTEGER DEFAULT 0,
    rate_limit_per_minute INTEGER DEFAULT 10,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    revoked_at TIMESTAMP,
    revoked_by UUID REFERENCES users(id)
);

-- API key permissions (for fine-grained control) - PHASE 2
-- CREATE TABLE api_key_permissions (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
--     permission_type ENUM('collections', 'disbursements', 'balance_check', 'webhook') NOT NULL,
--     is_granted BOOLEAN DEFAULT TRUE,
--     granted_at TIMESTAMP DEFAULT NOW()
-- );
```

#### 2.4 Financial/Transaction System (MVP Required)

```sql
-- Wallets (both virtual and real)
CREATE TABLE wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    wallet_type ENUM('virtual', 'real') NOT NULL,
    environment ENUM('sandbox', 'production') NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'XAF',
    status ENUM('active', 'frozen', 'closed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Transactions with better tracking
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    api_key_id UUID REFERENCES api_keys(id),
    wallet_id UUID REFERENCES wallets(id),
    
    -- Transaction details
    transaction_type ENUM('collection', 'disbursement', 'fee', 'refund') NOT NULL,
    external_id VARCHAR(255) NOT NULL, -- Client's reference
    internal_reference VARCHAR(255) UNIQUE NOT NULL, -- Our reference
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'XAF',
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Status tracking
    status ENUM('pending', 'processing', 'success', 'failed', 'cancelled') NOT NULL,
    momo_transaction_id VARCHAR(255),
    
    -- Environment and type
    environment ENUM('sandbox', 'production') DEFAULT 'sandbox',
    is_virtual BOOLEAN DEFAULT TRUE,
    
    -- Metadata
    description TEXT,
    request_data JSONB,
    response_data JSONB,
    error_code VARCHAR(50),
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Transaction status history - PHASE 2
-- CREATE TABLE transaction_status_history (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
--     old_status VARCHAR(50),
--     new_status VARCHAR(50),
--     status_reason TEXT,
--     changed_at TIMESTAMP DEFAULT NOW()
-- );
```

#### 2.5 Basic System Monitoring (MVP Required)
*Essential for rate limiting and basic analytics*

```sql
-- API usage tracking
CREATE TABLE api_usage_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    api_key_id UUID REFERENCES api_keys(id),
    
    -- Request details
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    request_body JSONB,
    response_status INTEGER,
    response_body JSONB,
    response_time_ms INTEGER,
    
    -- Client details
    ip_address INET,
    user_agent TEXT,
    environment ENUM('sandbox', 'production'),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW()
);

-- Rate limiting tracking - PHASE 2
-- CREATE TABLE rate_limit_violations (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     user_id UUID REFERENCES users(id),
--     api_key_id UUID REFERENCES api_keys(id),
--     ip_address INET,
--     endpoint VARCHAR(255),
--     violation_count INTEGER DEFAULT 1,
--     window_start TIMESTAMP,
--     created_at TIMESTAMP DEFAULT NOW()
-- );

-- System notifications - PHASE 2
-- CREATE TABLE notifications (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     user_id UUID REFERENCES users(id),
--     notification_type ENUM('verification', 'transaction', 'security', 'system') NOT NULL,
--     title VARCHAR(255) NOT NULL,
--     message TEXT NOT NULL,
--     is_read BOOLEAN DEFAULT FALSE,
--     priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
--     created_at TIMESTAMP DEFAULT NOW(),
--     read_at TIMESTAMP
-- );
```

### 🔧 Phase 2 - Production Enhancement Models
*These models add advanced functionality after MTN approval*

#### 2.6 Advanced Audit & History Tracking

```sql
#### 2.8 System Configuration & Management

```sql
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value JSONB NOT NULL,
    description TEXT,
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Support tickets (for user help)
CREATE TABLE support_tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
    assigned_to UUID REFERENCES users(id), -- Admin assigned
    created_at TIMESTAMP DEFAULT NOW(),
    resolved_at TIMESTAMP
);
```

## 3. Data Relationships & Flow

### 3.1 User Journey Data Flow

```
1. User Registration:
   users → student_profiles → verification_requests → verification_documents

2. Verification Process:
   verification_requests → verification_history → users.account_status

3. API Access:
   users → api_keys → api_key_permissions → wallets

4. Transaction Flow:
   api_keys → transactions → wallets → transaction_status_history

5. Monitoring:
   api_keys → api_usage_logs → rate_limit_violations → notifications
```

### 3.2 Key Relationships

**One-to-One Relationships:**
- users ↔ student_profiles
- users ↔ admin_profiles

**One-to-Many Relationships:**
- users → api_keys (one user can have multiple API keys)
- users → wallets (sandbox wallet + production wallet)
- users → transactions
- users → verification_requests
- verification_requests → verification_documents
- transactions → transaction_status_history

**Many-to-Many Relationships:**
- api_keys ↔ api_key_permissions (via junction table)

## 4. Data Validation & Constraints

### 4.1 Business Rules in Database

```sql
-- Ensure students have verification requests
ALTER TABLE users ADD CONSTRAINT check_student_verification 
CHECK (
    user_type != 'student' OR 
    id IN (SELECT user_id FROM verification_requests)
);

-- Ensure API keys belong to verified users
ALTER TABLE api_keys ADD CONSTRAINT check_verified_user
CHECK (
    user_id IN (
        SELECT user_id FROM verification_requests 
        WHERE status = 'approved'
    )
);

-- Ensure transaction amounts are positive
ALTER TABLE transactions ADD CONSTRAINT check_positive_amount
CHECK (amount > 0);

-- Ensure virtual wallets only in sandbox
ALTER TABLE wallets ADD CONSTRAINT check_virtual_environment
CHECK (
    (wallet_type = 'virtual' AND environment = 'sandbox') OR
    (wallet_type = 'real' AND environment = 'production')
);
```

### 4.2 Indexes for Performance

```sql
-- User lookups
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_type_status ON users(user_type, account_status);

-- API key lookups
CREATE INDEX idx_api_keys_user ON api_keys(user_id);
CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);
CREATE INDEX idx_api_keys_status ON api_keys(status);

-- Transaction queries
CREATE INDEX idx_transactions_user ON transactions(user_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_date ON transactions(created_at);
CREATE INDEX idx_transactions_external_id ON transactions(external_id);

-- Verification lookups
CREATE INDEX idx_verification_user ON verification_requests(user_id);
CREATE INDEX idx_verification_status ON verification_requests(status);

-- Analytics queries
CREATE INDEX idx_api_usage_user_date ON api_usage_logs(user_id, created_at);
CREATE INDEX idx_api_usage_endpoint ON api_usage_logs(endpoint);
```

## 5. Missing Data in Current PRD

### What Was Missing:

1. **Proper User Role Separation**: Admin vs Student data
2. **Verification System**: Separate tables for KYC workflow
3. **Document Management**: File storage and metadata
4. **Audit Trails**: History tracking for important changes
5. **API Permissions**: Fine-grained access control
6. **System Configuration**: Settings and configuration data
7. **Notifications**: User communication system
8. **Support System**: Help desk functionality
9. **Rate Limiting Data**: Violation tracking
10. **Proper Indexing**: Performance optimization

### What Was Good:

1. **Basic user structure** was correct
2. **Transaction concept** was right
3. **API key approach** was solid
4. **Environment separation** was planned well

## 6. Implementation Priority

### 🚀 MVP Phase (Required for MTN Demo):
**Core Tables (Must Build First):**
- users, student_profiles, admin_profiles
- verification_requests, verification_documents  
- api_keys, wallets, transactions
- api_usage_logs

**MVP Justification:**
- **Users & Profiles**: Core authentication and user management
- **Verification System**: Essential for student verification workflow  
- **API Keys & Wallets**: Required for API access and sandbox simulation
- **Transactions**: Core payment functionality demonstration
- **Usage Logs**: Basic monitoring and rate limiting

### 🔧 Phase 2 (Post-MTN Approval):
**Enhancement Tables:**
- verification_history, transaction_status_history
- api_key_permissions, rate_limit_violations
- notifications, support_tickets  
- system_settings

**Production Justification:**
- **Audit Trails**: Professional compliance and debugging
- **Advanced Permissions**: Fine-grained access control
- **User Communication**: Automated notifications and support
- **System Management**: Configuration and monitoring tools

### 📊 Phase 3 (Scale & Optimize):
- Advanced indexing strategies
- Data archiving and cleanup procedures
- Performance monitoring tables
- Business intelligence views

This data model provides a solid foundation that can grow with your application while maintaining data integrity and performance.