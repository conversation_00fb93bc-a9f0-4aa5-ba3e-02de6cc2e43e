'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { prisma } from '@/lib/prisma'

export async function signup(formData: FormData) {
  const supabase = await createClient()

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
    full_name: formData.get('fullName') as string,
    phone_number: formData.get('phoneNumber') as string,
  }

  const { data: authData, error } = await supabase.auth.signUp({
    email: data.email,
    password: data.password,
    options: {
      data: {
        full_name: data.full_name,
        phone_number: data.phone_number,
      },
      emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/login`,
    },
  })

  if (error) {
    console.error('Signup error:', error)
    redirect('/error')
  }

  // Create profile record in Prisma if auth user was created successfully
  if (authData.user) {
    try {
      await prisma.user.create({
        data: {
          id: authData.user.id,
          role: "STUDENT",   // from UserType enum
          studentProfile: {
            create: {
              fullName: data.full_name,
              email: data.email,
              phoneNumber: data.phone_number,
            },
          },


        },
      })
      console.log('Profile created successfully for user:', authData.user.id)
    } catch (profileError) {
      console.error('Failed to create profile:', profileError)
      // Note: We don't redirect to error here because the auth user was created successfully
      // The profile creation failure is logged but doesn't block the signup flow
    }
  }

  revalidatePath('/', 'layout')
  redirect('/register/success')
}
