import Link from "next/link"
import { CheckCircle, Mail } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"

export default function AdminRegisterSuccessPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 flex items-center justify-center py-12 bg-gradient-to-b from-white to-primary-50">
        <div className="container px-4 md:px-6">
          <Card className="mx-auto max-w-md">
            <CardHeader className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <CardTitle className="text-2xl font-bold text-green-700">
                Admin Registration Successful!
              </CardTitle>
              <CardDescription className="text-base">
                Your admin account has been created successfully.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center space-x-2 text-blue-600">
                  <Mail className="w-5 h-5" />
                  <span className="font-medium">Check Your Email</span>
                </div>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  We've sent a confirmation email to your registered email address.
                  Please click the link in the email to verify your account. After verification, you'll be redirected to the admin login page.
                </p>
              </div>
              <div className="space-y-3">
                <Button variant="outline" asChild className="w-full">
                  <Link href="/">
                    Back to Home
                  </Link>
                </Button>
              </div>
              <div className="text-center">
                <p className="text-xs text-muted-foreground">
                  Didn't receive the email?{" "}
                  <Link href="/admin/register" className="text-primary hover:underline">
                    Try registering again
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  )
}
