"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

const navigation = [
  {
    title: "Getting Started",
    items: [
      { title: "Introduction", href: "/docs" },
      { title: "Quick Start", href: "/docs/quick-start" },
      { title: "Sandbox Environment", href: "/sandbox", external: true },
    ],
  },
  {
    title: "Authentication",
    items: [
      { title: "API Keys", href: "/docs/authentication/api-keys" },
      { title: "OAuth", href: "/docs/authentication/oauth" },
      { title: "Webhooks", href: "/docs/authentication/webhooks" },
    ],
  },
  {
    title: "API Reference",
    items: [
      { title: "Payments", href: "/docs/api/payments" },
      { title: "Transactions", href: "/docs/api/transactions" },
      { title: "Customers", href: "/docs/api/customers" },
      { title: "Refunds", href: "/docs/api/refunds" },
    ],
  },
  {
    title: "Guides",
    items: [
      { title: "E-commerce Integration", href: "/docs/guides/ecommerce" },
      { title: "Mobile Apps", href: "/docs/guides/mobile" },
      { title: "Subscription Billing", href: "/docs/guides/subscriptions" },
    ],
  },
  {
    title: "Resources",
    items: [
      { title: "Error Codes", href: "/docs/resources/errors" },
      { title: "Rate Limits", href: "/docs/resources/rate-limits" },
      { title: "SDKs", href: "/docs/resources/sdks" },
      { title: "Support", href: "/docs/resources/support" },
    ],
  },
]

export function DocsNav() {
  const pathname = usePathname()

  return (
    <nav className="w-64 border-r bg-gray-50/50 p-6">
      <div className="space-y-6">
        {navigation.map((section) => (
          <div key={section.title}>
            <h3 className="mb-2 text-sm font-semibold text-gray-900">{section.title}</h3>
            <ul className="space-y-1">
              {section.items.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center gap-2 rounded-md px-2 py-1.5 text-sm transition-colors hover:bg-gray-100",
                      pathname === item.href
                        ? "bg-primary-100 text-primary-700 font-medium"
                        : "text-gray-600 hover:text-gray-900",
                    )}
                    {...(item.external && { target: "_blank", rel: "noopener noreferrer" })}
                  >
                    {item.title}
                    {item.external && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-700 text-xs">
                        Try It
                      </Badge>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </nav>
  )
}
