"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon, Wallet, ArrowUpRight, ArrowDownLeft, RefreshCw } from "lucide-react"

interface WalletData {
  id: string
  balance: number
  currency: string
  createdAt: string
  recentTransactions: Transaction[]
}

interface Transaction {
  id: string
  externalId: string
  amount: number
  currency: string
  status: string
  type: string
  createdAt: string
  completedAt: string | null
}

export default function WalletPage() {
  const [walletData, setWalletData] = useState<WalletData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch wallet data
  useEffect(() => {
    fetchWalletData()
  }, [])

  const fetchWalletData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch('/api/sandbox/wallet')
      if (!response.ok) {
        throw new Error('Failed to fetch wallet data')
      }

      const result = await response.json()
      if (result.success) {
        setWalletData(result.data)
      } else {
        throw new Error(result.error || 'Failed to fetch wallet data')
      }
    } catch (err) {
      console.error('Error fetching wallet data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load wallet data')
    } finally {
      setIsLoading(false)
    }
  }

  // Format wallet activity from transactions
  const walletActivity = walletData?.recentTransactions.map(transaction => ({
    id: transaction.id,
    type: "credit", // All sandbox transactions are collections (credits)
    amount: transaction.amount,
    currency: transaction.currency,
    description: `${transaction.type.toLowerCase()} transaction`,
    timestamp: transaction.createdAt,
    transactionId: transaction.id,
  })) || []

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    // Use a fixed format rather than locale-dependent formatting to avoid hydration errors
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
  }



  // Reset wallet - refresh data
  const handleResetWallet = () => {
    if (confirm("Are you sure you want to refresh your wallet data?")) {
      fetchWalletData()
    }
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading wallet data...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="h-8 w-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-900 font-medium mb-2">Failed to load wallet</p>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={fetchWalletData} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Virtual Wallet</h1>
        <p className="text-gray-500">Manage your test funds for sandbox transactions</p>
      </div>

      {/* Sandbox Environment Notice */}
      <div className="mb-6 rounded-lg bg-blue-50 border border-blue-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <InfoIcon className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-blue-800">🧪 Sandbox Wallet</p>
            <p className="text-sm text-blue-700 mt-1">
              This is a virtual wallet for testing. No real money is involved. Balance reflects your sandbox transactions.
            </p>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Wallet Balance Card */}
        <Card className="md:col-span-2">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Sandbox Wallet Balance</CardTitle>
                <CardDescription>Your virtual test funds</CardDescription>
              </div>
              <Wallet className="h-8 w-8 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <div className="text-5xl font-bold mb-2">
                {walletData ? walletData.balance.toLocaleString() : '0'} {walletData?.currency || 'XAF'}
              </div>
              <p className="text-gray-500 mb-4">Available Test Funds</p>
              <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                SANDBOX CURRENCY
              </Badge>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row gap-3">
            <Button variant="outline" className="w-full sm:w-auto" onClick={handleResetWallet}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Data
            </Button>
          </CardFooter>
        </Card>

        {/* Quick Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Wallet Stats</CardTitle>
            <CardDescription>Sandbox wallet activity</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Last Transaction</span>
                <span className="text-sm font-medium">{formatDate(walletActivity[0].timestamp)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Transactions Count</span>
                <span className="text-sm font-medium">{walletActivity.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Total Credited</span>
                <span className="text-sm font-medium">
                  {walletActivity
                    .filter(item => item.type === 'credit')
                    .reduce((sum, item) => sum + item.amount, 0)
                    .toLocaleString()} XAF
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Total Debited</span>
                <span className="text-sm font-medium">
                  {walletActivity
                    .filter(item => item.type === 'debit')
                    .reduce((sum, item) => sum + item.amount, 0)
                    .toLocaleString()} XAF
                </span>
              </div>
            </div>

            <Alert className="bg-yellow-50 border-yellow-200">
              <AlertTitle className="text-yellow-800 flex items-center">
                <RefreshCw className="h-4 w-4 mr-2" /> Unlimited Funds
              </AlertTitle>
              <AlertDescription className="text-yellow-700 text-xs">
                In sandbox mode, you can add as much virtual currency as needed for testing purposes.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      {/* Transaction History */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Wallet Activity</CardTitle>
          <CardDescription>Your virtual wallet transaction history</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Transaction ID</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {walletActivity.map((activity) => (
                  <TableRow key={activity.id}>
                    <TableCell>{formatDate(activity.timestamp)}</TableCell>
                    <TableCell>{activity.description}</TableCell>
                    <TableCell>
                      {activity.type === "credit" ? (
                        <div className="flex items-center text-success">
                          <ArrowDownLeft className="h-4 w-4 mr-1" />
                          Credit
                        </div>
                      ) : (
                        <div className="flex items-center text-destructive">
                          <ArrowUpRight className="h-4 w-4 mr-1" />
                          Debit
                        </div>
                      )}
                    </TableCell>
                    <TableCell className={activity.type === "credit" ? "text-success" : "text-destructive"}>
                      {activity.type === "credit" ? "+" : "-"}{activity.amount.toLocaleString()} {activity.currency}
                    </TableCell>
                    <TableCell className="font-mono text-xs">
                      {activity.transactionId || "—"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>


    </div>
  )
}