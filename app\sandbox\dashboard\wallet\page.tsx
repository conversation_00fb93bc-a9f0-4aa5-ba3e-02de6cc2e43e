"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon, Wallet, ArrowUpRight, ArrowDownLeft, RefreshCw } from "lucide-react"

export default function WalletPage() {
  const [walletBalance, setWalletBalance] = useState(100000)
  const [isAddingFunds, setIsAddingFunds] = useState(false)
  const [fundAmount, setFundAmount] = useState("")

  // Mock wallet activity
  const walletActivity = [
    {
      id: "wallet_txn_001",
      type: "credit",
      amount: 10000,
      currency: "XAF",
      description: "Added test funds",
      timestamp: "2025-06-15T14:30:00Z",
      transactionId: "test_txn_001",
    },
    {
      id: "wallet_txn_002",
      type: "debit",
      amount: 5000,
      currency: "XAF",
      description: "Test disbursement",
      timestamp: "2025-06-15T13:45:00Z",
      transactionId: "test_txn_002",
    },
    {
      id: "wallet_txn_003",
      type: "credit",
      amount: 15000,
      currency: "XAF",
      description: "Added test funds",
      timestamp: "2025-06-15T12:30:00Z",
      transactionId: null,
    },
    {
      id: "wallet_txn_004",
      type: "credit",
      amount: 30000,
      currency: "XAF",
      description: "Initial test balance",
      timestamp: "2025-06-15T10:00:00Z",
      transactionId: null,
    }
  ]

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    // Use a fixed format rather than locale-dependent formatting to avoid hydration errors
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
  }

  // Add test funds
  const handleAddTestFunds = () => {
    if (fundAmount && Number.parseInt(fundAmount) > 0) {
      setWalletBalance((prev) => prev + Number.parseInt(fundAmount))
      setIsAddingFunds(false)
      setFundAmount("")
      // In a real app, we'd add this to the activity list
    }
  }

  // Reset wallet
  const handleResetWallet = () => {
    if (confirm("Are you sure you want to reset your wallet balance to 100,000 XAF?")) {
      setWalletBalance(100000)
      // In a real app, we'd update the activity list
    }
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Virtual Wallet</h1>
        <p className="text-gray-500">Manage your test funds for sandbox transactions</p>
      </div>

      {/* Sandbox Environment Notice */}
      <div className="mb-6 rounded-lg bg-blue-50 border border-blue-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <InfoIcon className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-blue-800">🧪 Sandbox Wallet</p>
            <p className="text-sm text-blue-700 mt-1">
              This is a virtual wallet for testing. No real money is involved. Add as much test funds as you need.
            </p>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Wallet Balance Card */}
        <Card className="md:col-span-2">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Sandbox Wallet Balance</CardTitle>
                <CardDescription>Your virtual test funds</CardDescription>
              </div>
              <Wallet className="h-8 w-8 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <div className="text-5xl font-bold mb-2">{walletBalance.toLocaleString()} XAF</div>
              <p className="text-gray-500 mb-4">Available Test Funds</p>
              <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                SANDBOX CURRENCY
              </Badge>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row gap-3">
            <Button className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700" onClick={() => setIsAddingFunds(true)}>
              Add Test Funds
            </Button>
            <Button variant="outline" className="w-full sm:w-auto" onClick={handleResetWallet}>
              Reset Wallet
            </Button>
          </CardFooter>
        </Card>

        {/* Quick Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Wallet Stats</CardTitle>
            <CardDescription>Sandbox wallet activity</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Last Transaction</span>
                <span className="text-sm font-medium">{formatDate(walletActivity[0].timestamp)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Transactions Count</span>
                <span className="text-sm font-medium">{walletActivity.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Total Credited</span>
                <span className="text-sm font-medium">
                  {walletActivity
                    .filter(item => item.type === 'credit')
                    .reduce((sum, item) => sum + item.amount, 0)
                    .toLocaleString()} XAF
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Total Debited</span>
                <span className="text-sm font-medium">
                  {walletActivity
                    .filter(item => item.type === 'debit')
                    .reduce((sum, item) => sum + item.amount, 0)
                    .toLocaleString()} XAF
                </span>
              </div>
            </div>

            <Alert className="bg-yellow-50 border-yellow-200">
              <AlertTitle className="text-yellow-800 flex items-center">
                <RefreshCw className="h-4 w-4 mr-2" /> Unlimited Funds
              </AlertTitle>
              <AlertDescription className="text-yellow-700 text-xs">
                In sandbox mode, you can add as much virtual currency as needed for testing purposes.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>

      {/* Transaction History */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Wallet Activity</CardTitle>
          <CardDescription>Your virtual wallet transaction history</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Transaction ID</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {walletActivity.map((activity) => (
                  <TableRow key={activity.id}>
                    <TableCell>{formatDate(activity.timestamp)}</TableCell>
                    <TableCell>{activity.description}</TableCell>
                    <TableCell>
                      {activity.type === "credit" ? (
                        <div className="flex items-center text-success">
                          <ArrowDownLeft className="h-4 w-4 mr-1" />
                          Credit
                        </div>
                      ) : (
                        <div className="flex items-center text-destructive">
                          <ArrowUpRight className="h-4 w-4 mr-1" />
                          Debit
                        </div>
                      )}
                    </TableCell>
                    <TableCell className={activity.type === "credit" ? "text-success" : "text-destructive"}>
                      {activity.type === "credit" ? "+" : "-"}{activity.amount.toLocaleString()} {activity.currency}
                    </TableCell>
                    <TableCell className="font-mono text-xs">
                      {activity.transactionId || "—"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Test Funds Dialog */}
      {isAddingFunds && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96">
            <h3 className="text-lg font-medium mb-4">Add Test Funds</h3>
            <p className="text-sm text-gray-600 mb-4">
              Add virtual funds to your sandbox wallet for testing transactions.
            </p>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Amount (XAF)</label>
                <Input
                  type="number"
                  value={fundAmount}
                  onChange={(e) => setFundAmount(e.target.value)}
                  placeholder="Enter amount"
                  min="1000"
                  max="1000000"
                  step="1000"
                />
                <p className="text-xs text-gray-500 mt-1">Minimum: 1,000 XAF, Maximum: 1,000,000 XAF</p>
              </div>
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => setIsAddingFunds(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddTestFunds} className="bg-blue-600 hover:bg-blue-700">
                  Add Funds
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}