import Image from "next/image"

export function Innovation() {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-b from-white to-primary-50">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-12">
          <div className="space-y-2">
            <div className="inline-block rounded-full bg-primary-100 px-3 py-1 text-sm font-medium text-primary-700">
              Innovation Hub
            </div>
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              Laying the Foundation for Student Innovation
            </h2>
            <p className="mx-auto max-w-[700px] text-gray-600 md:text-xl/relaxed">
              We're building the infrastructure that enables Cameroonian students to create the next generation of
              fintech solutions.
            </p>
          </div>
        </div>

        <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
          <div className="relative">
            <div className="absolute -inset-1 rounded-xl bg-gradient-to-r from-primary-600 to-secondary blur-xl opacity-30"></div>
            <div className="relative rounded-xl overflow-hidden shadow-2xl">
              <Image
                src="/placeholder.svg?height=400&width=500&text=Student+Innovation"
                alt="Student Innovation"
                width={500}
                height={400}
                className="object-cover"
              />
            </div>
          </div>
          <div className="space-y-6">
            <div className="space-y-2">
              <div className="inline-flex items-center space-x-2">
                <div className="h-4 w-4 rounded-full bg-primary-600"></div>
                <h3 className="text-xl font-bold">From Classroom to Market</h3>
              </div>
              <p className="text-gray-600">
                Transform your academic projects into real-world applications with production-ready payment
                infrastructure. StarterPay bridges the gap between classroom learning and market-ready solutions.
              </p>
            </div>
            <div className="space-y-2">
              <div className="inline-flex items-center space-x-2">
                <div className="h-4 w-4 rounded-full bg-primary-600"></div>
                <h3 className="text-xl font-bold">Technical Workshops</h3>
              </div>
              <p className="text-gray-600">
                Gain access to exclusive workshops on payment integration, API development, and fintech best practices
                led by industry experts and successful entrepreneurs.
              </p>
            </div>
            <div className="space-y-2">
              <div className="inline-flex items-center space-x-2">
                <div className="h-4 w-4 rounded-full bg-primary-600"></div>
                <h3 className="text-xl font-bold">Innovation Challenges</h3>
              </div>
              <p className="text-gray-600">
                Participate in regular hackathons and innovation challenges with prizes and opportunities to showcase
                your solutions to potential investors and industry partners.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
