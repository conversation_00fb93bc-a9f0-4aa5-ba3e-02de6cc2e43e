# Migration Guide: Mock Data to Real Data Integration

## 🎯 Overview

This guide covers the migration from mock data to real API integration in the StarterPay sandbox dashboard. Use this if you're updating an existing installation or understanding the changes made.

## 📋 What Changed

### ✅ Removed Features
- **Add Funds API**: `/api/sandbox/wallet` POST endpoint removed
- **Add Funds UI**: All "Add Funds" buttons and dialogs removed
- **Mock Data**: All hardcoded transaction and wallet data removed
- **Static API Keys**: Replaced with dynamic database-driven keys

### ✅ Added Features
- **Real Data Fetching**: All pages now fetch live data from database
- **Loading States**: Proper loading spinners and error handling
- **API Key Management**: Full CRUD operations for API keys
- **Transaction Listing**: New `/api/sandbox/transactions` endpoint
- **Error Recovery**: Retry buttons and graceful error handling

## 🔄 File Changes

### Modified Files

#### Frontend Components
```
app/sandbox/dashboard/page.tsx
- Added useEffect for data fetching
- Replaced mock data with API calls
- Added loading and error states
- Updated interfaces for real data

app/sandbox/dashboard/api-access/page.tsx
- Complete rewrite for real API key management
- Added create API key functionality
- Implemented proper security (masked keys)
- Added usage statistics display

app/sandbox/dashboard/wallet/page.tsx
- Removed add funds functionality
- Connected to real wallet API
- Added loading states and error handling
- Updated transaction display

app/sandbox/dashboard/transactions/page.tsx
- Removed all mock transaction data
- Connected to real transactions API
- Updated filtering and search logic
- Enhanced transaction details view
```

#### Backend Services
```
lib/sandbox-service.ts
- Removed addFunds method
- Enhanced error handling
- Improved data validation

app/api/sandbox/wallet/route.ts
- Removed POST endpoint (add funds)
- Enhanced GET endpoint response
- Added proper error handling

app/api/sandbox/api-keys/route.ts
- Enhanced for real API key management
- Added proper validation
- Improved security measures
```

#### New Files
```
app/api/sandbox/transactions/route.ts
- New endpoint for transaction listing
- Supports pagination and filtering
- Proper authentication and authorization
```

## 🛠️ Database Requirements

### Required Tables
Ensure these Prisma models exist in your schema:

```prisma
model SandboxWallet {
  id        String   @id @default(cuid())
  userId    String   @unique
  balance   Decimal  @default(0)
  currency  String   @default("XAF")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  user         User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions SandboxTransaction[]
}

model SandboxApiKey {
  id          String   @id @default(cuid())
  userId      String
  keyName     String
  keyHash     String
  environment String   @default("sandbox")
  isActive    Boolean  @default(true)
  usageCount  Int      @default(0)
  rateLimit   Int      @default(100)
  createdAt   DateTime @default(now())
  lastUsedAt  DateTime?
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model SandboxTransaction {
  id               String    @id @default(cuid())
  userId           String
  externalId       String
  amount           Decimal
  currency         String    @default("XAF")
  type             String    @default("COLLECTION")
  status           String    @default("PENDING")
  mtnTransactionId String?
  mtnReferenceId   String    @unique
  payerPhone       String?
  payerMessage     String?
  payeeNote        String?
  failureReason    String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  completedAt      DateTime?
  
  user   User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  wallet SandboxWallet @relation(fields: [userId], references: [userId])
}
```

## 🔧 Migration Steps

### 1. Update Dependencies
```bash
# Ensure you have the required packages
pnpm install uuid bcryptjs zod @types/uuid
```

### 2. Database Migration
```bash
# Generate and apply Prisma migrations
npx prisma generate
npx prisma db push
```

### 3. Update Environment Variables
```env
# Ensure your database connection is properly configured
DATABASE_URL="your_database_connection_string"
```

### 4. Test the Migration
```bash
# Build the project to check for errors
pnpm run build

# Start development server
pnpm run dev
```

## 🧪 Testing the Changes

### Frontend Testing
1. **Dashboard Overview**
   - Should show loading spinner initially
   - Should display real wallet balance
   - Should show actual API key status
   - Should handle errors gracefully

2. **API Access Page**
   - Should allow creating new API keys
   - Should list existing keys (masked)
   - Should show usage statistics
   - Should handle creation errors

3. **Wallet Page**
   - Should display real balance
   - Should show transaction history
   - Should have refresh functionality
   - No "Add Funds" button should exist

4. **Transactions Page**
   - Should list real transactions
   - Should support search and filtering
   - Should show detailed transaction info
   - Should handle empty states

### API Testing
```bash
# Test wallet endpoint
curl -X GET http://localhost:3000/api/sandbox/wallet

# Test API keys endpoint
curl -X GET http://localhost:3000/api/sandbox/api-keys

# Test transactions endpoint
curl -X GET http://localhost:3000/api/sandbox/transactions
```

## 🚨 Common Issues

### Issue: "Property 'sandboxApiKey' does not exist"
**Solution**: Run `npx prisma generate` to update the Prisma client

### Issue: Loading states never resolve
**Solution**: Check API endpoints are returning proper JSON responses

### Issue: Authentication errors
**Solution**: Ensure `withStudentAuth` middleware is properly configured

### Issue: Database connection errors
**Solution**: Verify `DATABASE_URL` environment variable is set correctly

## 🔄 Rollback Plan

If you need to rollback to mock data:

1. **Restore Mock Data**: Add back hardcoded data arrays
2. **Remove API Calls**: Replace `useEffect` hooks with static data
3. **Restore Add Funds**: Re-add the add funds functionality
4. **Remove Loading States**: Simplify components to not handle loading

## 📊 Performance Considerations

### Before (Mock Data)
- ✅ Instant loading (no API calls)
- ❌ No real data
- ❌ No persistence
- ❌ No user isolation

### After (Real Data)
- ✅ Real data from database
- ✅ User-specific data
- ✅ Persistent across sessions
- ✅ Proper error handling
- ⚠️ Slightly slower initial load (API calls)

## 🎯 Next Steps After Migration

1. **Monitor Performance**: Check API response times
2. **Add Caching**: Consider Redis for frequently accessed data
3. **Implement Webhooks**: Add real-time transaction updates
4. **Add Analytics**: Track API usage and user behavior
5. **Security Audit**: Review authentication and authorization

## 📚 Additional Resources

- [Sandbox Dashboard Guide](./sandbox-dashboard-guide.md)
- [API Reference](./sandbox-api-reference.md)
- [Database Schema](./starterpay_data_model.md)
- [Troubleshooting Guide](./troubleshooting.md)

---

**Migration Completed**: December 2024  
**Version**: Mock Data v1.0 → Real Data v2.0
