import { NextRequest, NextResponse } from 'next/server'
import { withStudentAuth } from '@/lib/api-auth'
import { SandboxService, SandboxError } from '@/lib/sandbox-service'
import { z } from 'zod'

const createApiKeySchema = z.object({
  keyName: z.string().min(1).max(50)
})

/**
 * POST /api/sandbox/api-keys
 * Create a new sandbox API key
 */
export const POST = withStudentAuth(async (request, auth) => {
  try {
    console.log('API Key creation started for user:', auth.user.id)

    const body = await request.json()
    console.log('Request body:', body)

    const { keyName } = createApiKeySchema.parse(body)
    console.log('Validated keyName:', keyName)

    const sandboxService = new SandboxService()
    console.log('SandboxService created')

    const apiKey = await sandboxService.createApiKey({
      userId: auth.user.id,
      keyName
    })
    console.log('API key created successfully:', { id: apiKey.id, keyName: apiKey.keyName })

    return NextResponse.json({
      success: true,
      data: apiKey,
      message: 'API key created successfully. Save this key securely - it will not be shown again.'
    })

  } catch (error) {
    console.error('Create API key error:', error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation error',
          details: error.errors
        },
        { status: 400 }
      )
    }

    if (error instanceof SandboxError) {
      return NextResponse.json(
        { 
          error: error.message,
          code: error.code
        },
        { status: error.statusCode }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
})

/**
 * GET /api/sandbox/api-keys
 * List user's API keys (without the actual key values)
 */
export const GET = withStudentAuth(async (request, auth) => {
  try {
    const { prisma } = await import('@/lib/prisma')
    
    const apiKeys = await prisma.sandboxApiKey.findMany({
      where: { userId: auth.user.id },
      select: {
        id: true,
        keyName: true,
        environment: true,
        isActive: true,
        usageCount: true,
        rateLimit: true,
        createdAt: true,
        lastUsedAt: true
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({
      success: true,
      data: apiKeys
    })

  } catch (error) {
    console.error('List API keys error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
})
