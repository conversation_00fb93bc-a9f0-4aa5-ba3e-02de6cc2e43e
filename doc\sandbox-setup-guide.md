# StarterPay Sandbox Setup Guide

## 🎯 **What We've Built**

A complete MTN MoMo Collections API integration for sandbox environment that allows students to collect payments in their applications.

## 🏗️ **Architecture**

```
Student App → StarterPay API → MTN MoMo Sandbox → StarterPay DB → Response
```

## 📦 **Components Created**

### **1. Database Models** ✅
- `SandboxWallet` - Virtual balance tracking
- `SandboxApiKey` - Student API key management
- `SandboxTransaction` - Collections transaction tracking

### **2. Core Services** ✅
- `MTNMoMoService` - Direct MTN MoMo API integration
- `SandboxService` - Business logic and database management

### **3. API Endpoints** ✅
- `POST /api/sandbox/collect-payment` - Main collection endpoint
- `GET /api/sandbox/transactions/{id}` - Transaction status
- `POST /api/sandbox/api-keys` - Generate API keys
- `GET /api/sandbox/api-keys` - List API keys
- `GET /api/sandbox/wallet` - Get wallet balance
- `POST /api/sandbox/wallet/add-funds` - Add virtual funds

## 🚀 **Setup Instructions**

### **Step 1: Environment Variables**

Add to your `.env` file:
```env
# MTN MoMo Configuration
MTN_MOMO_SUBSCRIPTION_KEY=your_subscription_key_here
MTN_MOMO_USER_ID=your_user_id_here
MTN_MOMO_API_KEY=your_api_key_here
```

**To get these values:**
1. Go to [MTN MoMo Developer Portal](https://momodeveloper.mtn.com/)
2. Create account and subscribe to Collections API
3. Get your subscription key from profile page
4. Run the app - it will auto-generate USER_ID and API_KEY on first use

### **Step 2: Database Migration**

```bash
npx prisma db push
npx prisma generate
```

### **Step 3: Install Dependencies**

```bash
npm install uuid bcryptjs zod @types/uuid @types/bcryptjs
```

### **Step 4: Test the System**

1. **Start the app:**
   ```bash
   npm run dev
   ```

2. **Login as a student** and go to sandbox dashboard

3. **Generate API key:**
   ```bash
   curl -X POST http://localhost:3000/api/sandbox/api-keys \
     -H "Authorization: Bearer YOUR_SESSION_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"keyName": "My Test Key"}'
   ```

4. **Test payment collection:**
   ```bash
   curl -X POST http://localhost:3000/api/sandbox/collect-payment \
     -H "Authorization: Bearer YOUR_SANDBOX_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "amount": 1000,
       "currency": "XAF",
       "externalId": "test_123",
       "payerPhone": "************",
       "payerMessage": "Test payment"
     }'
   ```

## 📋 **API Documentation**

### **For Students (External API)**

**Base URL:** `https://your-domain.com/api/sandbox`

**Authentication:** Bearer token (sandbox API key)

#### **Collect Payment**
```http
POST /collect-payment
Authorization: Bearer sk_sandbox_xxxxx
Content-Type: application/json

{
  "amount": 1000,
  "currency": "XAF",
  "externalId": "order_123",
  "payerPhone": "************",
  "payerMessage": "Payment for order #123",
  "payeeNote": "E-commerce purchase"
}
```

#### **Check Transaction Status**
```http
GET /transactions/{transactionId}
Authorization: Bearer sk_sandbox_xxxxx
```

### **For Dashboard (Internal API)**

**Authentication:** Student session token

#### **Generate API Key**
```http
POST /api-keys
Authorization: Bearer SESSION_TOKEN
Content-Type: application/json

{
  "keyName": "My App Key"
}
```

#### **Get Wallet Balance**
```http
GET /wallet
Authorization: Bearer SESSION_TOKEN
```

## 🔄 **Flow Example**

1. **Student creates API key** in sandbox dashboard
2. **Student integrates** collection API in their app
3. **Customer makes payment** in student's app
4. **Student's app calls** StarterPay API
5. **StarterPay calls** MTN MoMo sandbox
6. **Payment processed** and status returned
7. **Wallet balance updated** in sandbox

## 🧪 **Testing**

### **Test Phone Numbers (MTN Sandbox)**
- `************` - Always successful
- `237987654321` - Always fails
- Any other number - Random success/failure

### **Test Amounts**
- Any amount between 1 and 1,000,000 XAF
- Currency must be "XAF"

## 🔧 **Next Steps**

1. **Update Frontend** - Replace mock data in sandbox dashboard
2. **Add Real Forms** - Transaction creation and API key management
3. **Real-time Updates** - WebSocket or polling for transaction status
4. **Documentation** - API docs page for students
5. **Rate Limiting** - Implement proper rate limiting
6. **Webhooks** - Callback URL handling

## 🚨 **Important Notes**

- **Sandbox only** - No real money involved
- **XAF currency** - Cameroon Central African Franc
- **Phone format** - Must be 237XXXXXXXX
- **Token expiry** - MTN tokens expire in 1 hour (auto-refreshed)
- **Rate limits** - 100 requests per hour per API key

## 🎯 **Success Criteria**

✅ Students can generate sandbox API keys  
✅ Students can integrate collection API  
✅ Real MTN MoMo sandbox transactions work  
✅ Transaction status tracking functions  
✅ Virtual wallet balance updates  
⏳ Dashboard shows real transaction data (next step)

The core MTN MoMo integration is complete and ready for testing!
